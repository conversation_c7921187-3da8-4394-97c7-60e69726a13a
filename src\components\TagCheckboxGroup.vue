<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2025-06-24 10:51:12
 * @LastEditors: 景 彡
-->
<template>
  <div class="flex flex-wrap gap-2">
    <div
      v-for="item in options"
      :key="item.value"
      class="cursor-pointer select-none border rounded-md border-solid px-3 py-1 transition-all duration-300 ease-in-out"
      :class="[
        isSelected(item.value)
          ? 'bg-blue-100 text-blue-600 border-blue-400 dark:(bg-blue-900/30 text-blue-300 border-blue-700)'
          : 'bg-white text-gray-700 border-gray-300 hover:(border-blue-500 text-blue-500)',
        disabled && 'opacity-60 cursor-not-allowed',
      ]"
      @click="handleClick(item.value)"
    >
      {{ item.label }}
    </div>
    <a-button v-if="!disabled" type="primary" ghost :icon="h(PlusOutlined)" @click="emit('add')" />
  </div>
</template>

<script setup lang="ts">
import { PlusOutlined } from '@ant-design/icons-vue'

interface Option {
  label: string
  value: string
}

const props = withDefaults(defineProps<{
  options: Option[]
  multiple?: boolean
  disabled?: boolean
}>(), {
  multiple: false,
  disabled: false,
})

const emit = defineEmits<{
  add: []
}>()

// 仅支持 string 或 string[]
const modelValue = defineModel<string | string[] | null>('value', {
  default: null,
  validator: (v, p: typeof props) => {
    if (v && p.multiple && !Array.isArray(v)) {
      console.warn('multiple 下 modelValue 必须是数组')
      return false
    }
    return true
  },
})

// 判断是否被选中
function isSelected(value: string): boolean {
  if (props.multiple) {
    return modelValue.value?.includes(value) || false
  }
  return modelValue.value === value
}

// 点击切换行为
function handleClick(value: string) {
  if (props.disabled)
    return

  if (props.multiple) {
    const current = Array.isArray(modelValue.value) ? [...modelValue.value] : []

    const index = current.indexOf(value)
    if (index > -1) {
      current.splice(index, 1)
    }
    else {
      current.push(value)
    }

    modelValue.value = current
  }
  else {
    modelValue.value = modelValue.value === value ? null : value
  }
}

// function handleClick(value: string) {
//   if (props.disabled)
//     return

//   if (props.multiple) {
//     const current = modelValue.value as Array<string>
//     const index = current.indexOf(value)
//     if (index > -1) {
//       current.splice(index, 1)
//     }
//     else {
//       current.push(value)
//     }
//     modelValue.value = current
//   }
//   else {
//     modelValue.value = modelValue.value === value ? null : value
//   }
// }
</script>
