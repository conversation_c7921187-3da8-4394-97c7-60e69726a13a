import { User } from "./User";
import { PushStatus } from "./PushStatus";
import { DeptPushChannelLog } from "./DeptPushChannelLog";
import { DeptMainPush } from "./DeptMainPush";
import { ReadingStatus } from "./ReadingStatus";
/**合作单位推送记录*/
export class DeptPushLog {
  /**推送时间*/
  createdTime: Dayjs = dayjs();
  /**用户*/
  createdUser?: User | null | undefined = null;
  /**推送的用户，系统自动推送时为null*/
  createdUserId?: GUID = null;
  /**被推送用户*/
  pushUser?: User | null | undefined = null;
  pushUserId: GUID = "00000000-0000-0000-0000-000000000000";
  status: PushStatus = 0;
  channels?: DeptPushChannelLog[] | null | undefined = [];
  /**关联的推送主体*/
  deptMainPushId: GUID = "00000000-0000-0000-0000-000000000000";
  /**单位推送主体*/
  deptMainPush?: DeptMainPush | null | undefined = null;
  /**站内阅读状态*/
  readingStatus: ReadingStatus = 0;
  /**用户*/
  readingStatusUser?: User | null | undefined = null;
  readingStatusUserId?: GUID = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
