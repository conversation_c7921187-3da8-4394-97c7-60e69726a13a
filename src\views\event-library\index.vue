<template>
  <div v-if="!hiddenSearch" class="mb-4 bg-bg-container p-16px">
    <c-search-form
      v-model:value="searchForm"
      :fields="visibleFields"
      :collapsed="true"
      :col="{ xs: 24, sm: 24, md: 24, lg: 6, xl: 6, xxl: 6, span: 6 }"
      @search="search"
    />
  </div>
  <div class="bg-bg-container">
    <div v-if="!hiddenTool" class="flex items-center justify-between p4">
      <div class="flex items-center">
        <a-radio-group v-model:value="searchForm.isReportJyt" @change="listRef?.fetchData">
          <a-radio-button
            key="查询全部"
            :value="null"
          >
            全部
          </a-radio-button>
          <a-radio-button
            key="未上报"
            :value="false"
          >
            未上报
          </a-radio-button>
          <a-radio-button
            key="已上报"
            :value="true"
          >
            已上报
          </a-radio-button>
        </a-radio-group>
      </div>

      <div>
        <a-button v-if="$auth([_Role.舆情监测人员, _Role.舆情监测中心领导])" type="primary" :icon="h(PlusOutlined)" @click="createOpen = true">录入</a-button>
        <a-button :icon="h(ExportOutlined)" class="ml-16px">导出</a-button>
      </div>
    </div>
    <c-list ref="listRef" :api="api.EventManage.GetListAsync" immediate pagination :get-params="searchForm">
      <template #renderItem="{ item, index }: {item:PublicEventViewModel, index: number }">
        <a-list-item :key="index">
          <div class="w-100% flex items-center justify-between">
            <div class="flex-1">
              <div class="ch2-list-title cursor-pointer items-center" @click="toDetail(item.id)">
                <div class="mb-2 block cursor-pointer text-lg text-gray-900 font-semibold leading-tight hover:text-primary">
                  {{ item.name }}
                </div>
                <div class="text-base">{{ item.title }}</div>
              </div>
              <div class="my-2">
                <a-tag :color="item.jytEntityId ? 'success' : 'error'">{{ item.jytEntityId ? '已上报' : '未上报' }}</a-tag>
                <span class="ml-2 mr-4 c-primary-text">
                  {{ item.carbonCopy?.map(item => item.department?.name || '').join('、') }}
                </span>
                <span class="c-text-secondary">创建时间：{{ dateTime(item.createdAt) }}</span>
                <span class="ml-4 c-text-secondary">创建人：{{ item.createdUser?.name || '--' }}</span>
              </div>
              <div v-if="item.trackContent" class="rounded bg-gray-50 p-2">
                <c-truncated-text :text="item.trackContent" :max-lines="1" class="text-sm text-gray-500" />
              </div>
            </div>
            <div class="ml-16px w-82px flex flex-col">
              <a-button type="primary" class="mb-8px" @click="toDetail(item.id)">查看</a-button>
              <a-dropdown v-if="$auth([_Role.舆情监测人员, _Role.舆情监测中心领导])">
                <template #overlay>
                  <a-menu @click="handleMenuClick($event, item)">
                    <a-menu-item
                      key="upReport"
                    >
                      {{ item.jytEntityId ? '上报教育厅' : '重新上报' }}
                    </a-menu-item>
                    <a-menu-item
                      key="del"
                    >
                      <span class="c-error">删除</span>
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button>
                  更多
                  <DownOutlined />
                </a-button>
              </a-dropdown>
            </div>
          </div>
        </a-list-item>
      </template>
    </c-list>
  </div>

  <a-drawer v-model:open="open" title="编辑事件" width="80%" @close="open = false">
    <template #extra>
      <a-button style="margin-right: 8px" @click="onClose">取消</a-button>
      <!-- <a-button type="primary" @click="onSave">保存</a-button> -->
      <a-button v-if="Guid.isNotNull(currentObj.id)" type="primary" @click="onSave">保存</a-button>
      <a-popconfirm
        v-else
        title="新增事件会直接提交到责任单位审核！"
        ok-text="确定"
        cancel-text="取消"
        placement="leftTop"
        @confirm="onSave"
      >
        <a-button type="primary">保存</a-button>
      </a-popconfirm>
    </template>
    <EditForm ref="editFormRef" v-model="currentObj" />
  </a-drawer>

  <a-drawer v-model:open="createOpen" title="新增事件" width="860" @close="createOpen = false">
    <template #extra>
      <a-button style="margin-right: 8px" @click="onClose">取消</a-button>
      <a-button type="primary" @click="onCreateSave">保存</a-button>
    </template>
    <CreateForm ref="createFormRef" v-model="addObj" />
  </a-drawer>

  <!-- 提交审核 -->
  <AuditModal v-model:open="auditOpen" v-model:jyt-entity-id="jytEntityId" :params="auditParams" title="确认提交到教育厅吗？" @submit="listRef?.fetchData" />
</template>

<script lang='ts' setup>
import type { PublicEventViewModel } from '@/api/models'
import type { Key } from 'ant-design-vue/lib/_util/type'
import type { SearchField } from 'ch2-components/types/search-form/types'
import * as api from '@/api'
import { AuditType, PublicEventCreateModel } from '@/api/models'
import { $auth } from '@/permission'
import { _Role } from '@/permission/RoleName'
import { Guid } from '@/utils/GUID'
import { DownOutlined, ExportOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import AuditModal from './components/AuditModal.vue'
import CreateForm from './components/CreateForm.vue'
import EditForm from './components/EditForm.vue'

defineProps<{
  hiddenSearch?: boolean
  hiddenTool?: boolean
  audit?: AuditType
}>()

definePage({
  meta: {
    title: '事件库',
    icon: 'ReadOutlined',
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '事件库',
        local: true,
        icon: 'ReadOutlined',
        order: 6,
      },
    },
  },
})

const addObj = ref(new PublicEventCreateModel())

const { auditOpen, jytEntityId, auditParams, onAudit } = useAuditHook()

const { open, onSave, editFormRef, createOpen, createFormRef, currentObj, onClose, onCreateSave } = useEditHook()

const listRef = useTemplateRef('listRef')

const searchForm = ref<Parameters<typeof api.EventManage.GetListAsync>[0]>({
  isReportJyt: false,
})

const visibleFields = reactive<SearchField[]>([
  { label: '信息标题', el: 'input', prop: 'title', attrs: { placeholder: '请输入内容' } },
  { label: '事件名称', el: 'input', prop: 'name', attrs: { placeholder: '请输入简介' } },

])

function search() {
  nextTick(listRef.value?.fetchData)
}

const router = useRouter()

const currentEventId = ref(Guid.empty)

function handleMenuClick(val: { key: Key }, record: PublicEventViewModel) {
  currentEventId.value = record.id

  if (val.key === 'del') {
    Modal.confirm({
      title: '确认删除该事件?',
      okText: '确认',
      cancelText: '取消',
      async onOk() {
        try {
          await api.EventManage.Delete_PostAsync({ id: record.id })
          listRef.value?.refreshData()
          message.success('删除成功')
        }
        catch (error: any) {
          message.error(error.message)
        }
      },
    })
  }
  else if (val.key === 'upReport') {
    onAudit(record, AuditType.处置)
  }
}

function toDetail(id: GUID) {
  router.push({ path: '/event-library/detail', query: { id } })
}

function useEditHook() {
  const open = ref(false)

  const editFormRef = useTemplateRef('editFormRef')

  const currentObj = ref(new PublicEventCreateModel())

  const createOpen = ref(false)

  const createFormRef = useTemplateRef('createFormRef')

  async function onSave() {
    await editFormRef.value?.onSubmit()
    listRef.value?.refreshData()
    message.success('保存成功')
    open.value = false
  }

  async function onCreateSave() {
    await createFormRef.value?.onSubmit()
    listRef.value?.refreshData()
    message.success('保存成功')
    createOpen.value = false
  }

  function onClose() {
    open.value = false
  }

  function onEdit(record: PublicEventViewModel) {
    currentObj.value = viewModelToEditModel(record, new PublicEventCreateModel())
    open.value = true
  }

  function onAdd() {
    createOpen.value = true
  }

  return { open, onSave, editFormRef, createOpen, createFormRef, currentObj, onClose, onEdit, onCreateSave, onAdd }
}

function useAuditHook() {
  const auditOpen = ref(false)

  const jytEntityId = ref(Guid.empty)

  const auditParams = ref({
    id: Guid.empty,
  })

  function onAudit(record: PublicEventViewModel, audit: AuditType) {
    console.log('%c [ audit ]-270', 'font-size:13px; background:pink; color:#bf2c9f;', audit)
    auditParams.value = { id: record.id }
    jytEntityId.value = record.jytEntityId
    auditOpen.value = true
  }

  return { auditOpen, jytEntityId, auditParams, onAudit }
}
</script>

<style scoped lang="less">
.ch2-list-title {
  .dp-text-ellipsis-wrapper {
    font-size: 16px;
    cursor: pointer;
    &:hover {
      color: @colorPrimary;
    }
  }
}
.dp-text-ellipsis-wrapper {
  margin-bottom: 8px;
}
</style>
