<template>
  <a-spin :spinning="spinning">
    <a-affix :offset-top="0">
      <div class="mb-16px w-full flex justify-between rounded-2 bg-bg-container px-4 py-3 shadow-sm">
        <div class="flex flex-1 flex-col gap-2">
          <div class="text-lg text-gray-800 font-medium">{{ detail.name }}</div>
          <!-- <div class="flex items-center gap-3">
            <a-button
              v-for="(item, index) in anchorItems"
              :key="index"
              type="link"
              class="anchor-btn text-sm !px-0"
              :class="{ active: activeAnchor === item.key }"
              @click="scrollToSection(item.key)"
            >
              {{ item.label }}
            </a-button>
          </div> -->
        </div>
        <div class="flex items-center gap-3">
          <div class="ml-4 border-l border-gray-200 pl-4">
            <div class="text-xs text-gray-500">状态</div>
            <div class="text-base text-gray-800 font-medium">
              {{ detail.jytEntityId ? '已上报' : '未上报' }}
            </div>
          </div>
        </div>
      </div>
    </a-affix>

    <a-space direction="vertical" class="w-full">
      <a-card id="basic-info" title="基本信息">
        <template #extra>
          <a-button
            v-if="$auth([_Role.舆情监测人员, _Role.舆情监测中心领导])"
            class="mr-16px" type="primary" ghost @click="onEdit"
          >
            编辑
          </a-button>
        </template>

        <a-descriptions
          bordered :label-style="{ width: '140px' }" :content-style="{ width: '300px' }"
          :column="{ sm: 1, md: 3 }" size="small"
        >
          <a-descriptions-item label="事件编号" :span="4" class="required">
            {{ detail.code }}
          </a-descriptions-item>
          <a-descriptions-item label="事件名称" :span="4" class="required">
            {{ detail.name }}
          </a-descriptions-item>
          <a-descriptions-item label="信息标题" :span="4" class="required">
            {{ detail.title }}
          </a-descriptions-item>
          <a-descriptions-item label="关联单位" :span="4" class="required">
            {{ detail.carbonCopy?.map(item => item.department?.name).join('，') }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间" :span="4" class="required">
            {{ dateTime(detail.createdAt) }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- <div class="grid grid-cols-[1fr_2fr] space-x-2">
        <a-card v-if="Guid.isNotNull(detail.deptId)" id="disposal-opinion" title="处理意见">
          {{ detail.trackContent }}
        </a-card>
      </div> -->

      <a-card id="ccCopy-opinion" title="关联单位">
        <template #extra>
          <a-button
            v-if="$auth([_Role.舆情监测人员, _Role.舆情监测中心领导])"
            type="primary" ghost @click="useCcCopy.add()"
          >
            添加单位
          </a-button>
        </template>
        <div v-if="detail.carbonCopy && detail.carbonCopy.length > 0" class="grid grid-cols-2 gap-3">
          <div v-for="(item, index) in detail.carbonCopy" :key="index" class="border-(1px #f0f0f0 solid)">
            <a-badge-ribbon :text="item.isPushDept ? '已提醒' : '未提醒'" :color="item.isPushDept ? 'green' : 'red'">
              <div class="group relative p-2">
                <div class="flex items-center">
                  <div class="mr-4 text-base">{{ item.department?.name }}</div>
                  <div v-if="$auth([_Role.舆情监测人员, _Role.舆情监测中心领导]) && item.department?.isCooperate">
                    <a @click="onReminder(item.departmentId)">
                      <c-icon-comment-outlined class="mr-1" :class="{ 'c-#58B869': item.isPushDept }" />{{ item.isPushDept ? '再次提醒' : '提醒' }}</a>
                  </div>
                </div>

                <div class="mt-1">
                  <span v-if="item.department?.isCooperate" class="mr-2 inline-block rounded-md bg-#F0B800 px-1 text-center text-3 c-#fff">合作</span>
                  <span>抄送时间：{{ dateTime(item.time) }}</span>
                  <!-- <span class="ml-4">阅读时间：{{ dateTime(item.auditAt) }}</span> -->
                </div>
                <div class="mt-1 flex c-text-secondary"> <div class="w-48px">备注：</div><c-truncated-text :text="item.context" :max-lines="2" class="text-sm text-gray-500" /></div>

                <div v-if="$auth([_Role.舆情监测人员, _Role.舆情监测中心领导])" class="absolute bottom-0 right-0 hidden group-hover:block">
                  <a-popconfirm title="确定移除此单位吗？" @confirm="useCcCopy.onDel(item.id)">
                    <template #icon><c-icon-question-circle-outlined style="color: red" /></template>
                    <c-icon-delete-outlined class="ml-8px cursor-pointer text-24px c-error" />
                  </a-popconfirm>
                </div>
              </div>
            </a-badge-ribbon>
          </div>
        </div>
        <p v-else>暂无抄送信息</p>
      </a-card>

      <a-card id="event-tracking" title="事件跟踪">
        <template #extra>
          <div v-if="detail.jytEntityId && $auth([_Role.舆情监测人员, _Role.舆情监测中心领导])" class="flex gap-2">
            <a-button
              type="primary" ghost @click="publicOpinionHook.add()"
            >
              添加舆情
            </a-button>
            <a-button
              type="primary" ghost @click="addtrackingReport"
            >
              录入事件续报
            </a-button>
            <a-button
              type="primary" ghost @click="addSpreadSituation"
            >
              录入传播态势
            </a-button>
          </div>
        </template>
        <a-timeline>
          <template v-for="(item, index) in displayedTimeline" :key="index">
            <!-- 关联舆情 -->
            <a-timeline-item v-if="item.type === 'publicOpinion'" color="red">
              <div class="flex justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-2 text-gray-500">
                    <span class="text-primary font-bold">关联舆情</span>
                    <span>{{ dateTime(item.createdAt) }}</span>
                  </div>

                  <div class="mt-2 space-y-2">
                    <div class="flex">
                      <span class="text-nowrap c-text-secondary"> 摘要：</span>   <c-truncated-text
                        class="cursor-pointer hover:c-primary-hover" :text="item.summary || ''" :max-lines="2"
                        type="launch" @click="publicOpinionHook.viewPublicOpinion(item)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </a-timeline-item>

            <!-- 跟踪报告 -->
            <a-timeline-item v-if="item.type === 'report'" color="blue">
              <div class="flex justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-2 text-gray-500">
                    <span class="text-primary font-bold">事件续报</span>
                    <span>{{ dateTime(item.created) }}</span>
                    <span>{{ item.createdBy?.userName }}</span>
                  </div>
                  <div class="mt-2 space-y-2">
                    <div class="flex">
                      <span class="text-nowrap c-text-secondary"> 摘要：</span>   <c-truncated-text class="w-0 flex-1" :text="item.abstract || ''" :max-lines="1" type="launch" />
                    </div>
                    <div class="flex">
                      <span class="text-nowrap c-text-secondary"> 内容：</span>  <c-truncated-text class="w-0 flex-1" :text="item.context || ''" :max-lines="1" type="launch" />
                    </div>
                    <div class="flex">
                      <span class="text-nowrap c-text-secondary">附件：</span>
                      <ViewFile class="w-0 flex-1" :files="item.attachmentFiles " />
                    </div>
                  </div>
                  <div class="mt-2">
                    <a-button v-if="$auth([_Role.舆情监测人员, _Role.舆情监测中心领导]) && !item.jytEntityId" type="primary" ghost size="small" @click="onResubmit(item.id)">上报教育厅</a-button>
                    <a-tag v-else color="success">已上报</a-tag>
                  </div>
                </div>
                <div v-if="$auth([_Role.舆情监测人员, _Role.舆情监测中心领导])">
                  <a-popconfirm title="确定删除此事件续报吗？" @confirm="deltrackingReportConfirm(item.id)">
                    <template #icon><c-icon-question-circle-outlined style="color: red" /></template>
                    <c-icon-delete-outlined class="ml-8px cursor-pointer text-20px c-error" />
                  </a-popconfirm>
                </div>
              </div>
            </a-timeline-item>

            <!-- 传播态势 -->
            <a-timeline-item v-if="item.type === 'spreadSituation'" color="green">
              <div class="flex justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-2 text-gray-500">
                    <span class="text-primary font-bold">传播态势</span>
                    <span>{{ dateTime(item.created) }}</span>
                    <span>{{ item.createdBy?.userName }}</span>
                  </div>
                  <div v-if="item.context" class="mt-2">
                    <c-image
                      :src="joinFilePathById(item.context)" alt="avatar" :preview="true"
                      style="height: 140px; width:140px ; object-fit:cover"
                    />
                  </div>
                  <div class="mt-2">
                    <a-button v-if="!item.jytEntityId" type="primary" ghost size="small" @click="onSpread(item.id)">上报教育厅</a-button>
                    <a-tag v-else color="success">已上报</a-tag>
                  </div>
                </div>
                <a-popconfirm v-if="$auth([_Role.舆情监测人员, _Role.舆情监测中心领导])" title="确定删除此传播态势吗？" @confirm="delSpreadSituationConfirm(item.id)">
                  <template #icon><c-icon-question-circle-outlined style="color: red" /></template>
                  <c-icon-delete-outlined class="ml-8px cursor-pointer text-20px c-error" />
                </a-popconfirm>
              </div>
            </a-timeline-item>
          </template>
        </a-timeline>
        <div v-if="mergedTimeline.length > 1" class="mt-4 text-center">
          <a-button type="link" @click="showTimeLineAll = !showTimeLineAll">
            {{ showTimeLineAll ? '收起' : '查看更多' }}
            <template #icon>
              <component :is="showTimeLineAll ? 'UpOutlined' : 'DownOutlined'" />
            </template>
          </a-button>
        </div>
        <a-empty v-if="!mergedTimeline.length" />
      </a-card>
    </a-space>
  </a-spin>

  <!-- 录入续报 -->
  <a-drawer
    v-model:open="trackingReportOpen" destroy-on-close title="录入事件续报" width="860px"
    @close="trackingReportOpen = false"
  >
    <c-pro-form
      v-model:value="trackingReportForm" :fields="trackingReportFields"
      :descriptions="{ column: 1, bordered: true }" @finish="ontrackingReporttSave"
    >
      <template #footer>
        <a-descriptions-item label="操作">
          <a-button type="primary" html-type="submit">
            保存
          </a-button>
        </a-descriptions-item>
      </template>
    </c-pro-form>
  </a-drawer>

  <!-- 录入传播态势 -->
  <a-drawer
    v-model:open="spreadSituationOpen" destroy-on-close title="录入传播态势" width="860px"
    @close="spreadSituationOpen = false"
  >
    <c-pro-form
      v-model:value="spreadSituationForm" :fields="spreadSituationFields"
      :descriptions="{ column: 1, bordered: true }" @finish="onSpreadSituationSave"
    >
      <template #context>
        <div v-if="spreadSituationForm.context" class="coverBox size-140px overflow-hidden">
          <c-image
            :src="joinFilePathById(spreadSituationForm.context)" alt="avatar" :preview="true" :del-ico="true"
            style="height: 140px; width:140px ; object-fit:cover" @del-image="() => spreadSituationForm.context = ''"
          />
        </div>
        <a-button v-else type="dashed" block style="width: 100px; height: 100px" @click="avatarUpload">
          <template #icon>
            <c-icon-plus-outlined />
          </template>
          上传
        </a-button>
      </template>
      <template #footer>
        <a-descriptions-item label="操作">
          <a-button type="primary" html-type="submit">
            保存
          </a-button>
        </a-descriptions-item>
      </template>
    </c-pro-form>
  </a-drawer>

  <!-- 舆情编辑 -->
  <OpinionEditForm
    v-model:open="publicOpinionHook.open.value" v-model="publicOpinionHook.formData.value"
    :event-name="detail.name!"
    :read-only="publicOpinionHook.readOnly.value" @save="publicOpinionHook.onSave"
  />

  <!-- 编辑事件 -->
  <c-modal v-model:open="editEventOpen" destroy-on-close full-modal full-screen>
    <CreateForm ref="editFormRef" v-model="currentEditForm" />
    <!-- <EditForm ref="editFormRef" v-model="currentEditForm" :attachment-file="detail.attachmentFile" /> -->
    <template #footer>
      <a-button style="margin-right: 8px" @click="onClose">取消</a-button>
      <a-button type="primary" @click="onSave">保存</a-button>
    </template>
  </c-modal>

  <a-drawer v-model:open="useCcCopy.ccCopyOpen.value" title="抄送" width="860" destroy-on-close @close="useCcCopy.onClose">
    <template #extra>
      <a-button style="margin-right: 8px" @click="useCcCopy.onClose">取消</a-button>
      <a-button type="primary" @click="useCcCopy.onSave">保存</a-button>
    </template>
    <MakeACopy ref="makeACopyRef" :even-id="detail.id" :current-dept-ids="currentDeptIds" />
  </a-drawer>

  <!-- 合作单位推送 -->
  <PushReminder v-model:open="pushReminderOpen" :current-department-id="currentDepartmentId" :entity-id="detail.id" :entity-type="DeptPushType.事件" @success="getData">
    <template #left>
      <div class="space-y-4">
        <div>
          <span class="c-text-base">事件名称：</span>
          <span class="c-text">{{ detail.name }}</span>
        </div>
        <div>
          <span class="c-text-base">信息标题：</span>
          <span class="c-text">{{ detail.title }}</span>
        </div>
        <div>
          <span class="c-text">创建时间：</span>
          <span class="c-text">{{ dateTime(detail.createdAt, 'YYYY-MM-DD HH:mm') }}</span>
        </div>
      </div>
    </template>
  </PushReminder>
</template>

<script lang='ts' setup>
import type { EventReportViewModel, EventSpreadSituationViewModel, PublicOpinionViewModel } from '@/api/models'
import type { FormField } from 'ch2-components/lib/pro-form/types'
import * as api from '@/api'
import { DeptPushType, EventReportEditModel, EventSpreadSituationEditModel, FileAttribution, FileType, PublicEventCreateModel, PublicEventViewModel, PublicOpinionEditModel } from '@/api/models'
import Upload from '@/components/FileManager/Upload.vue'
import { $auth } from '@/permission'
import { _Role } from '@/permission/RoleName'
import { Guid } from '@/utils/GUID'

import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { createVNode } from 'vue'
import { useRoute } from 'vue-router'
import OpinionEditForm from '../public-opinion/components/EditForm.vue'
// import EditForm from './components/EditForm.vue'
import CreateForm from './components/CreateForm.vue'
import MakeACopy from './components/MakeACopy.vue'

definePage({
  meta: {
    title: '舆情事件详情',
    icon: 'ReadOutlined',
    hidden: true,
    noCache: true,
  },
})

const useCcCopy = useCcCopyHook()

const { pushReminderOpen, currentDepartmentId, onReminder } = useReminderHook()

const { editFormRef, currentEditForm, editEventOpen, onSave, onClose, onEdit } = useEditFormHook()

const { trackingReportOpen, trackingReportFields, trackingReportForm, ontrackingReporttSave, addtrackingReport, deltrackingReportConfirm, onResubmit } = trackingReportHook()

const { spreadSituationOpen, spreadSituationFields, spreadSituationForm, onSpreadSituationSave, addSpreadSituation, delSpreadSituationConfirm, avatarUpload, onSpread } = spreadSituationHook()

const publicOpinionHook = usePublicOpinion()

const currentEventId = ref('')

const currentDeptIds = ref<GUID[]>([])

const detail = ref(new PublicEventViewModel())

type TimelineItem = ({
  type: 'publicOpinion'
  timestamp: number
  id: GUID
  priority: number
  [key: string]: any
} & PublicOpinionViewModel) | ({
  type: 'report'
  timestamp: number
  id: GUID
  priority: number
  [key: string]: any
} & EventReportViewModel)|({
  type: 'spreadSituation'
  timestamp: number
  id: GUID
  priority: number
} & EventSpreadSituationViewModel)

const showTimeLineAll = ref(false)

const mergedTimeline = computed(() => {
  const timeline: TimelineItem[] = []

  // 添加关联舆情
  if (detail.value.publicOpinions?.length) {
    detail.value.publicOpinions.forEach((item) => {
      timeline.push({
        ...item,
        type: 'publicOpinion',
        timestamp: dayjs(item.createdAt).toDate().getTime(),
        priority: 1,
      })
    })
  }

  // 添加跟踪报告
  if (detail.value.reports?.length) {
    detail.value.reports.forEach((item) => {
      timeline.push({
        ...item,
        type: 'report',
        timestamp: dayjs(item.created).toDate().getTime(),
        priority: 2,
      })
    })
  }

  // 添加传播态势
  if (detail.value.spreadSituation?.length) {
    detail.value.spreadSituation.forEach((item) => {
      timeline.push({
        ...item,
        type: 'spreadSituation',
        timestamp: dayjs(item.created).toDate().getTime(),
        priority: 3,
      })
    })
  }

  // 先按时间戳降序排序，再按优先级升序排序
  return timeline.sort((a, b) => {
    if (b.timestamp === a.timestamp) {
      return a.priority - b.priority
    }
    return b.timestamp - a.timestamp
  })
})

const displayedTimeline = computed(() => {
  if (showTimeLineAll.value) {
    return mergedTimeline.value
  }
  return mergedTimeline.value.slice(0, 1)
})

const spinning = ref(false)

const activeAnchor = ref('basic-info')

async function getData() {
  spinning.value = true
  try {
    detail.value = await api.EventManage.GetAsync({ id: currentEventId.value })
    viewModelToEditModel(detail.value, currentEditForm.value)
    currentDeptIds.value = detail.value.carbonCopy?.map(item => item.departmentId) || []
    spinning.value = false
  }
  catch (error: any) {
    message.error(error.message)
    spinning.value = false
  }
}

function useReminderHook() {
  const pushReminderOpen = ref(false)

  const currentDepartmentId = ref(Guid.empty)

  function onReminder(id: GUID) {
    currentDepartmentId.value = id
    pushReminderOpen.value = true
  }

  return { pushReminderOpen, currentDepartmentId, onReminder }
}

function usePublicOpinion() {
  const open = ref(false)
  const readOnly = ref(false)
  const formData = ref(new PublicOpinionEditModel())

  const add = () => {
    readOnly.value = false
    open.value = true
    formData.value = {
      ...new PublicOpinionEditModel(),
      eventId: detail.value.id,
    }
  }

  const viewPublicOpinion = (item: PublicOpinionEditModel) => {
    formData.value = item
    open.value = true
    readOnly.value = true
  }

  async function onSave() {
    getData()
    message.success('保存成功')
    open.value = false
  }

  function onClose() {
    formData.value = new PublicOpinionEditModel()
    open.value = false
  }

  return { open, add, onClose, formData, onSave, readOnly, viewPublicOpinion }
}

function useEditFormHook() {
  const editEventOpen = ref(false)

  // const currentEditForm = ref(new PublicEventEditModel())
  const currentEditForm = ref(new PublicEventCreateModel())

  const editFormRef = useTemplateRef('editFormRef')

  function onEdit() {
    viewModelToEditModel(detail.value, currentEditForm.value)
    currentEditForm.value.carbonCopy = detail.value.carbonCopy?.map(item => item.departmentId) || []
    editEventOpen.value = true
  }

  async function onSave() {
    await editFormRef.value?.onSubmit()
    message.success('保存成功')
    getData()
    editEventOpen.value = false
  }

  function onClose() {
    editEventOpen.value = false
  }

  return { open, editFormRef, currentEditForm, editEventOpen, onSave, onClose, onEdit }
}

function trackingReportHook() {
  const trackingReportOpen = ref(false)

  const trackingReportFields: FormField[] = [
    {
      label: '续报摘要',
      prop: 'abstract',
      el: 'input',
      required: '续报摘要必填',
      attrs: { rows: 4, showCount: true },
    },
    {
      label: '报告内容',
      prop: 'context',
      el: 'textarea',
      required: '报告内容必填',
      attrs: { rows: 4, showCount: true },
    },
    {
      label: '附件',
      prop: 'attachment',
      el: Upload,
      attrs: { rows: 4, showCount: true },
    },
  ]

  const trackingReportForm = ref(new EventReportEditModel())

  async function ontrackingReporttSave() {
    try {
      await api.EventManage.Report_PostAsync(trackingReportForm.value)
      getData()
      trackingReportOpen.value = false
      message.success('保存成功')
    }
    catch (error: any) {
      message.error(`保存失败：${error.message}`)
    }
  }

  function addtrackingReport() {
    trackingReportForm.value = new EventReportEditModel()
    trackingReportForm.value.eventId = detail.value.id as string
    trackingReportOpen.value = true
  }

  async function deltrackingReportConfirm(id: GUID) {
    try {
      await api.EventManage.RemoveReport_PostAsync({ id })
      getData()
      message.success('删除成功')
    }
    catch (error: any) {
      message.error(`删除失败：${error.message}`)
    }
  }

  function onResubmit(id: GUID) {
    Modal.confirm({
      title: '确定上报此续报到教育厅吗?',
      icon: createVNode(ExclamationCircleOutlined),
      async onOk() {
        await api.EventManage.ReportEventReport_PostAsync({ jytEntityId: detail.value.jytEntityId }, { id })
        message.success('上报成功')
      },
    })
  }

  return { trackingReportOpen, trackingReportFields, trackingReportForm, ontrackingReporttSave, addtrackingReport, deltrackingReportConfirm, onResubmit }
}

function spreadSituationHook() {
  const spreadSituationOpen = ref(false)

  const spreadSituationFields = ref([
    {
      label: '图片',
      prop: 'context',
      el: 'textarea',
      formItem: {
        rules: [{ required: true, message: '图片必填!' }],
      },
      attrs: { rows: 4, showCount: true },
    },
  ])

  const spreadSituationForm
  = ref(new EventSpreadSituationEditModel())

  async function onSpreadSituationSave() {
    try {
      await api
        .EventManage
        .EventSpreadSituation_PostAsync(spreadSituationForm.value)
      getData()
      spreadSituationOpen.value = false
      message.success('保存成功')
    }
    catch (error: any) {
      message.error(`保存失败：${error.message}`)
    }
  }

  function addSpreadSituation() {
    spreadSituationForm.value = new
    EventSpreadSituationEditModel()
    spreadSituationForm.value.eventId = detail.value.id as string
    spreadSituationOpen.value = true
  }

  async function delSpreadSituationConfirm(id: string) {
    try {
      await api.EventManage.RemoveEventSpreadSituation_PostAsync({ id })
      getData()
      message.success('删除成功')
    }
    catch (error: any) {
      message.error(`删除失败
      ：${error.message}`)
    }
  }

  function avatarUpload() {
    useFileMangerModal((files) => {
      spreadSituationForm.value.context
       = files[0]?.id
    }, { multiple: false, immediateReturn: true, menu: [FileType.图片], fileAttribution:
       FileAttribution.管理认证 })
  }

  function onSpread(id: GUID) {
    Modal.confirm({
      title: '确定上报此传播态势到教育厅吗?',
      icon: createVNode(ExclamationCircleOutlined),
      async onOk() {
        await api.EventManage.ReportSpreadSituation_PostAsync({ jytEntityId:
          detail.value.jytEntityId }, { id })
        message.success('上报成功')
      },
    })
  }

  return { spreadSituationOpen, spreadSituationFields, spreadSituationForm, onSpreadSituationSave, addSpreadSituation, delSpreadSituationConfirm, avatarUpload, onSpread }
}

function useCcCopyHook() {
  const ccCopyOpen = ref(false)

  const makeACopyRef = useTemplateRef('makeACopyRef')

  function add() {
    ccCopyOpen.value = true
  }

  async function onDel(id: string | GUID) {
    await api.EventManage
      .RemoveCarbonCopy_PostAsync({ id })
    message.success('删除成功')
    getData()
  }

  async function onSave() {
    await makeACopyRef.value?.onSave()
    message.success('保存成功')
    makeACopyRef.value?.onClose()
    ccCopyOpen.value = false
    getData()
  }

  function onClose() {
    makeACopyRef.value?.onClose()
    ccCopyOpen.value = false
  }

  return { ccCopyOpen, add, onSave, onClose, onDel }
}

const route = useRoute()

onMounted(() => {
  if (route.query?.id) {
    currentEventId.value = route.query.id as string
    getData()
  }

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        activeAnchor.value = entry.target.id
      }
    })
  }, {
    threshold: 0.5,
    rootMargin: '-100px 0px -100px 0px',
  })

  // 观察所有锚点区域
  const sections = ['basic-info', 'disposal-opinion', 'event-tracking', 'work-requirements', 'disposal-process']
  sections.forEach((id) => {
    const element = document.getElementById(id)
    if (element) {
      observer.observe(element)
    }
  })
})

// 修改滚动方法，添加偏移量
// function scrollToSection(sectionId: string) {
//   const element = document.getElementById(sectionId)
//   if (element) {
//     const offset = 100 // 与 scroll-margin-top 保持一致
//     const elementPosition = element.getBoundingClientRect().top
//     const offsetPosition = elementPosition + window.pageYOffset - offset

//     window.scrollTo({
//       top: offsetPosition,
//       behavior: 'smooth',
//     })
//   }
// }
</script>

<style scoped lang="less">
.ant-btn-link.active::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: @colorPrimary;
  border-radius: 1px;
  transition: all 0.3s;
}

.ant-card {
  scroll-margin-top: 16px;
}

.anchor-btn {
  @apply relative;

  &.active::after {
    content: '';
    @apply absolute bottom-0 left-0 w-full h-0.5 bg-primary;
  }
}
</style>
