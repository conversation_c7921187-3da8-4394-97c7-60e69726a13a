<template>
  <div class="">
    <div v-if="!hiddenSelectDept" class="flex items-center">
      <span class="c-text-base">推送单位：</span>
      <C2TreeSelect

        v-model:value="currentDeptId"
        :read-only="!isEdit"
        :api="() => api.DepartmentManage.GetAllDepartmentsAsync({ isCooperate: true })"
        class="w-280px"
        @change="handleDeptChange"
      />
      <c-icon-form-outlined
        v-show="!isEdit"
        class="ml-2 cursor-pointer text-4 text-primary"
        @click="handleEditToggle"
      />
    </div>

    <a-spin :spinning="spinning">
      <template v-if="userData.length > 0">
        <div v-for="(item, index) in userData" :key="index" class="mt-4 space-y-2">
          <div>
            <span class="c-text-base">推送对象{{ index + 1 }}：</span>
            <span class="c-text">{{ item.name }}</span>
          </div>
          <div>
            <span class="c-text-base">推送渠道：</span>
            <span class="c-text">
              <a-checkbox-group
                v-model:value="channelMap[item.id!]"
                :options="channelOptions"
              />
            </span>
          </div>
        </div>
      </template>
      <a-empty
        v-else
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
        description="暂无用户数据"
      />
    </a-spin>

    <a-divider />

    <div>
      <a-checkbox v-model:checked="force">
        <span class="text-base">追加发送</span>
      </a-checkbox>
      <span class="c-error">存在推送记录请选择追加推送，推送记录不存在则默认。</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { UserViewModel } from '@/api/models'
import * as api from '@/api'
import { DeptPushChannel } from '@/api/models'
import { Empty } from 'ant-design-vue'

interface Props {
  currentDeptId?: GUID
  force?: boolean
  hiddenSelectDept?: boolean
  
}

interface Emits {
  (e: 'update:currentDeptId', value: GUID): void
  (e: 'update:force', value: boolean): void
  (e: 'dataChange', data: { userData: UserViewModel[], channelMap: Record<string, DeptPushChannel[]> }): void
}

const props = withDefaults(defineProps<Props>(), {
  currentDeptId: Guid.empty,
  force: false,
})

const emit = defineEmits<Emits>()

// 内部状态管理
const userData = ref<UserViewModel[]>([])
const channelMap = ref<Record<string, DeptPushChannel[]>>({})
const spinning = ref(false)
const isEdit = ref(false)

// 双向绑定的计算属性
const currentDeptId = computed({
  get: () => props.currentDeptId,
  set: value => emit('update:currentDeptId', value),
})

const force = computed({
  get: () => props.force,
  set: value => emit('update:force', value),
})

// 推送渠道选项
const channelOptions = ref([
  { label: '微信', value: DeptPushChannel.微信 },
  { label: '电话', value: DeptPushChannel.电话 },
  { label: '邮箱', value: DeptPushChannel.邮箱 },
])

// 数据加载函数
async function getUserData() {
  if (!currentDeptId.value || currentDeptId.value === Guid.empty) {
    return
  }

  spinning.value = true
  try {
    userData.value = await api.DeptPushLogs.GetPushUserAsync({ departmentId: currentDeptId.value })
    channelMap.value = {}
    userData.value.forEach((e) => {
      channelMap.value[e.id!] = e.deptPushChannels || []
    })

    // 向父组件传递数据变化
    emit('dataChange', {
      userData: userData.value,
      channelMap: channelMap.value,
    })

    spinning.value = false
  }
  catch (error) {
    console.log('%c [ error ]-getUserData', 'font-size:13px; background:pink; color:#bf2c9f;', error)
    spinning.value = false
  }
}

function handleDeptChange() {
  getUserData()
}

function handleEditToggle() {
  isEdit.value = !isEdit.value
}

// 监听部门ID变化
watch(() => props.currentDeptId, (newDeptId) => {
  if (newDeptId && newDeptId !== Guid.empty) {
    currentDeptId.value = newDeptId
    getUserData()
  }
}, { immediate: true })

// 监听渠道映射变化，同步到父组件
watch(channelMap, (newChannelMap) => {
  emit('dataChange', {
    userData: userData.value,
    channelMap: newChannelMap,
  })
}, { deep: true })
</script>
