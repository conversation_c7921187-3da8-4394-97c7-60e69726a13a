/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/[...404]': RouteRecordInfo<'/[...404]', '/:404(.*)', { 404: ParamValue<true> }, { 404: ParamValue<false> }>,
    '/ai-analysis/': RouteRecordInfo<'/ai-analysis/', '/ai-analysis', Record<never, never>, Record<never, never>>,
    '/contact-address-book/': RouteRecordInfo<'/contact-address-book/', '/contact-address-book', Record<never, never>, Record<never, never>>,
    '/daily-report/': RouteRecordInfo<'/daily-report/', '/daily-report', Record<never, never>, Record<never, never>>,
    '/early-warning/': RouteRecordInfo<'/early-warning/', '/early-warning', Record<never, never>, Record<never, never>>,
    '/event-library/': RouteRecordInfo<'/event-library/', '/event-library', Record<never, never>, Record<never, never>>,
    '/event-library/detail': RouteRecordInfo<'/event-library/detail', '/event-library/detail', Record<never, never>, Record<never, never>>,
    '/file-managers/': RouteRecordInfo<'/file-managers/', '/file-managers', Record<never, never>, Record<never, never>>,
    '/full-text-search/': RouteRecordInfo<'/full-text-search/', '/full-text-search', Record<never, never>, Record<never, never>>,
    '/full-text-search/list': RouteRecordInfo<'/full-text-search/list', '/full-text-search/list', Record<never, never>, Record<never, never>>,
    '/home': RouteRecordInfo<'/home', '/home', Record<never, never>, Record<never, never>>,
    '/login/': RouteRecordInfo<'/login/', '/login', Record<never, never>, Record<never, never>>,
    '/login/login': RouteRecordInfo<'/login/login', '/login/login', Record<never, never>, Record<never, never>>,
    '/logoutReload': RouteRecordInfo<'/logoutReload', '/logoutReload', Record<never, never>, Record<never, never>>,
    '/public-opinion/': RouteRecordInfo<'/public-opinion/', '/public-opinion', Record<never, never>, Record<never, never>>,
    '/public-opinion-review/': RouteRecordInfo<'/public-opinion-review/', '/public-opinion-review', Record<never, never>, Record<never, never>>,
    '/public-opinion-review/history': RouteRecordInfo<'/public-opinion-review/history', '/public-opinion-review/history', Record<never, never>, Record<never, never>>,
    '/push-records/': RouteRecordInfo<'/push-records/', '/push-records', Record<never, never>, Record<never, never>>,
    '/push-records/cooperate': RouteRecordInfo<'/push-records/cooperate', '/push-records/cooperate', Record<never, never>, Record<never, never>>,
    '/push-records/detail': RouteRecordInfo<'/push-records/detail', '/push-records/detail', Record<never, never>, Record<never, never>>,
    '/push-records/template-management': RouteRecordInfo<'/push-records/template-management', '/push-records/template-management', Record<never, never>, Record<never, never>>,
    '/report-to-jyt/': RouteRecordInfo<'/report-to-jyt/', '/report-to-jyt', Record<never, never>, Record<never, never>>,
    '/special-submission/': RouteRecordInfo<'/special-submission/', '/special-submission', Record<never, never>, Record<never, never>>,
    '/statistical-analysis/': RouteRecordInfo<'/statistical-analysis/', '/statistical-analysis', Record<never, never>, Record<never, never>>,
    '/system-manage/client-log/': RouteRecordInfo<'/system-manage/client-log/', '/system-manage/client-log', Record<never, never>, Record<never, never>>,
    '/system-manage/manage': RouteRecordInfo<'/system-manage/manage', '/system-manage/manage', Record<never, never>, Record<never, never>>,
    '/system-manage/role/': RouteRecordInfo<'/system-manage/role/', '/system-manage/role', Record<never, never>, Record<never, never>>,
    '/system-manage/standard/': RouteRecordInfo<'/system-manage/standard/', '/system-manage/standard', Record<never, never>, Record<never, never>>,
    '/system-manage/user/': RouteRecordInfo<'/system-manage/user/', '/system-manage/user', Record<never, never>, Record<never, never>>,
    '/type-tag-management/': RouteRecordInfo<'/type-tag-management/', '/type-tag-management', Record<never, never>, Record<never, never>>,
    '/type-tag-management/source': RouteRecordInfo<'/type-tag-management/source', '/type-tag-management/source', Record<never, never>, Record<never, never>>,
    '/user-center/': RouteRecordInfo<'/user-center/', '/user-center', Record<never, never>, Record<never, never>>,
    '/user-center/user-center': RouteRecordInfo<'/user-center/user-center', '/user-center/user-center', Record<never, never>, Record<never, never>>,
  }
}
