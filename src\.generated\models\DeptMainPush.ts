import { DeptPushType } from "./DeptPushType";
import { Department } from "./Department";
import { DeptPushLog } from "./DeptPushLog";
/**单位推送主体*/
export class DeptMainPush {
  /**关联数据id*/
  entityId?: GUID = null;
  /**推送类型*/
  entityType: DeptPushType = 0;
  /**推送摘要*/
  abstract?: string | null | undefined = null;
  /**被推送的单位*/
  pushDept?: Department | null | undefined = null;
  pushDeptId: GUID = "00000000-0000-0000-0000-000000000000";
  /**创建时间*/
  recentlyPushTime: Dayjs = dayjs();
  deptPushLogs?: DeptPushLog[] | null | undefined = [];
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
