<template>
  <a-card title="推送记录" style="width: 100%">
    <c-table 
      :columns="columns" 
      :data-source="tableData" 
      size="small" 
      :pagination="false" 
      :loading="loading" 
      :scroll="{ y: 520 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'deptPushLogs'">
          <div v-for="(item, index) in record.deptPushLogs" :key="item.id">
            <span>({{ index + 1 }})</span>
            <span>{{ record.abstract }}；</span>
            <span>
              【<b>{{ item.pushUser?.name }}</b> —— 
              {{ item.channels?.map((v: DeptPushChannelLog) => 
                (`${DeptPushChannel[v.channel]}-${PushStatus[v.status]}`)).join('、') }}】
            </span>
            <span 
              class="mx-2" 
              :class="getStatusClass(item.status)"
            >
              {{ PushStatus[item.status] }}
            </span>
          </div>
        </template>
        <template v-if="column.key === 'recentlyPushTime'">
          <div>{{ dateTime(record.recentlyPushTime) }}</div>
        </template>
      </template>
    </c-table>
  </a-card>
</template>

<script lang="ts" setup>
import type { DeptMainPush, DeptPushChannelLog } from '@/api/models'
import { DeptPushChannel, PushStatus } from '@/api/models'

interface Props {
  tableData: DeptMainPush[]
  loading: boolean
}

defineProps<Props>()

// 表格列配置
const columns = ref([
  {
    title: '推送信息',
    dataIndex: 'deptPushLogs',
    key: 'deptPushLogs',
  },
  {
    title: '推送时间',
    dataIndex: 'recentlyPushTime',
    key: 'recentlyPushTime',
    width: 200,
  },
])

// 获取状态样式类
function getStatusClass(status: number) {
  if (status === PushStatus.失败) {
    return 'c-error'
  } else if (status === PushStatus.成功) {
    return 'c-success'
  }
  return ''
}
</script>
