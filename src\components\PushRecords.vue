<template>
  <div>
    <a-card title="推送记录" style="width: 100%">
      <c-table
        :columns="columns"
        :data-source="tableData"
        size="small"
        :pagination="false"
        :loading="loading"
        :scroll="{ y: 520 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'deptPushLogs'">
            <div v-for="(item, index) in record.deptPushLogs" :key="item.id">
              <span>({{ index + 1 }})</span>
              <span>{{ record.abstract }}；</span>
              <span>
                【<b>{{ item.pushUser?.name }}</b> ——
                {{ item.channels?.map((v: DeptPushChannelLog) =>
                  (`${DeptPushChannel[v.channel]}-${PushStatus[v.status]}`)).join('、') }}】
              </span>
              <span
                class="mx-2"
                :class="getStatusClass(item.status)"
              >
                {{ PushStatus[item.status] }}
              </span>
            </div>
          </template>
          <template v-if="column.key === 'recentlyPushTime'">
            <div>{{ dateTime(record.recentlyPushTime) }}</div>
          </template>
        </template>
      </c-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import type { DeptMainPush, DeptPushChannelLog, DeptPushType } from '@/api/models'
import * as api from '@/api'
import { DeptPushChannel, PushStatus } from '@/api/models'

interface Props {
  currentDeptId?: GUID
  entityId?: GUID
  entityType?: DeptPushType
}

const props = withDefaults(defineProps<Props>(), {
  currentDeptId: Guid.empty,
})

// 内部状态管理
const tableData = ref<DeptMainPush[]>([])
const loading = ref(false)

// 表格列配置
const columns = ref([
  {
    title: '推送信息',
    dataIndex: 'deptPushLogs',
    key: 'deptPushLogs',
  },
  {
    title: '推送时间',
    dataIndex: 'recentlyPushTime',
    key: 'recentlyPushTime',
    width: 200,
  },
])

// 数据加载函数
async function getUserPushStatus() {
  if (!props.currentDeptId || props.currentDeptId === Guid.empty || !props.entityId || !props.entityType) {
    return
  }

  loading.value = true
  try {
    tableData.value = await api.DeptPushLogs.GetUserPushStatusAsync({
      departmentId: props.currentDeptId,
      entityId: props.entityId,
      entityType: props.entityType,
    })
    loading.value = false
  }
  catch (error) {
    console.log('%c [ error ]-getUserPushStatus', 'font-size:13px; background:pink; color:#bf2c9f;', error)
    loading.value = false
  }
}

// 获取状态样式类
function getStatusClass(status: number) {
  if (status === PushStatus.失败) {
    return 'c-error'
  }
  else if (status === PushStatus.成功) {
    return 'c-success'
  }
  return ''
}

// 监听 props 变化，自动加载数据
watch([() => props.currentDeptId, () => props.entityId, () => props.entityType], () => {
  getUserPushStatus()
}, { immediate: true })

// 暴露刷新方法给父组件
defineExpose({
  refresh: getUserPushStatus,
})
</script>
