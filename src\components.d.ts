/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AAffix: typeof import('ant-design-vue/es')['Affix']
    AAlert: typeof import('ant-design-vue/es')['Alert']
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    ABackTop: typeof import('ant-design-vue/es')['BackTop']
    ABadge: typeof import('ant-design-vue/es')['Badge']
    ABadgeRibbon: typeof import('ant-design-vue/es')['BadgeRibbon']
    ABreadcrumb: typeof import('ant-design-vue/es')['Breadcrumb']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACheckboxGroup: typeof import('ant-design-vue/es')['CheckboxGroup']
    ACol: typeof import('ant-design-vue/es')['Col']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    ADescriptions: typeof import('ant-design-vue/es')['Descriptions']
    ADescriptionsItem: typeof import('ant-design-vue/es')['DescriptionsItem']
    ADirectoryTree: typeof import('ant-design-vue/es')['DirectoryTree']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADrawer: typeof import('ant-design-vue/es')['Drawer']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AImage: typeof import('ant-design-vue/es')['Image']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutFooter: typeof import('ant-design-vue/es')['LayoutFooter']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AList: typeof import('ant-design-vue/es')['List']
    AListItem: typeof import('ant-design-vue/es')['ListItem']
    AListItemMeta: typeof import('ant-design-vue/es')['ListItemMeta']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuDivider: typeof import('ant-design-vue/es')['MenuDivider']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    APagination: typeof import('ant-design-vue/es')['Pagination']
    APopconfirm: typeof import('ant-design-vue/es')['Popconfirm']
    APopover: typeof import('ant-design-vue/es')['Popover']
    AProgress: typeof import('ant-design-vue/es')['Progress']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    AResult: typeof import('ant-design-vue/es')['Result']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASkeleton: typeof import('ant-design-vue/es')['Skeleton']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    AStatisticCountdown: typeof import('ant-design-vue/es')['StatisticCountdown']
    AStyleProvider: typeof import('ant-design-vue/es')['StyleProvider']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    AsyncButton: typeof import('./components/AsyncButton.vue')['default']
    ATableColumn: typeof import('ant-design-vue/es')['TableColumn']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATimeline: typeof import('ant-design-vue/es')['Timeline']
    ATimelineItem: typeof import('ant-design-vue/es')['TimelineItem']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATree: typeof import('ant-design-vue/es')['Tree']
    ATreeSelect: typeof import('ant-design-vue/es')['TreeSelect']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    AUploadDragger: typeof import('ant-design-vue/es')['UploadDragger']
    C2Tag: typeof import('./components/C2Tag.vue')['default']
    C2TreeSelect: typeof import('./components/C2TreeSelect.vue')['default']
    CBooleanSelect: typeof import('ch2-components/lib/boolean-select')['BooleanSelect']
    CButton: typeof import('ch2-components/lib/button')['Button']
    CConfigProvider: typeof import('ch2-components/lib/config-provider')['ConfigProvider']
    CDatePicker: typeof import('ch2-components/lib/date-picker')['DatePicker']
    CEllipsis: typeof import('ch2-components/lib/ellipsis')['Ellipsis']
    CEnumSelect: typeof import('ch2-components/lib/enum')['EnumSelect']
    CForm: typeof import('ch2-components/lib/form')['Form']
    CFormItem: typeof import('ch2-components/lib/form')['FormItem']
    CIconAppstoreAddOutlined: typeof import('@ant-design/icons-vue')['AppstoreAddOutlined']
    CIconCheckOutlined: typeof import('@ant-design/icons-vue')['CheckOutlined']
    CIconCloseOutlined: typeof import('@ant-design/icons-vue')['CloseOutlined']
    CIconCommentOutlined: typeof import('@ant-design/icons-vue')['CommentOutlined']
    CIconCustomerServiceOutlined: typeof import('@ant-design/icons-vue')['CustomerServiceOutlined']
    CIconDeleteOutlined: typeof import('@ant-design/icons-vue')['DeleteOutlined']
    CIconDownloadOutlined: typeof import('@ant-design/icons-vue')['DownloadOutlined']
    CIconDownOutlined: typeof import('@ant-design/icons-vue')['DownOutlined']
    CIconEditOutlined: typeof import('@ant-design/icons-vue')['EditOutlined']
    CIconEyeOutlined: typeof import('@ant-design/icons-vue')['EyeOutlined']
    CIconFieldTimeOutlined: typeof import('@ant-design/icons-vue')['FieldTimeOutlined']
    CIconFileMarkdownOutlined: typeof import('@ant-design/icons-vue')['FileMarkdownOutlined']
    CIconFilePptOutlined: typeof import('@ant-design/icons-vue')['FilePptOutlined']
    CIconFileZipOutlined: typeof import('@ant-design/icons-vue')['FileZipOutlined']
    CIconFormOutlined: typeof import('@ant-design/icons-vue')['FormOutlined']
    CIconInboxOutlined: typeof import('@ant-design/icons-vue')['InboxOutlined']
    CIconLeftOutlined: typeof import('@ant-design/icons-vue')['LeftOutlined']
    CIconLockOutlined: typeof import('@ant-design/icons-vue')['LockOutlined']
    CIconMenuOutlined: typeof import('@ant-design/icons-vue')['MenuOutlined']
    CIconPaperClipOutlined: typeof import('@ant-design/icons-vue')['PaperClipOutlined']
    CIconPictureOutlined: typeof import('@ant-design/icons-vue')['PictureOutlined']
    CIconPlaySquareOutlined: typeof import('@ant-design/icons-vue')['PlaySquareOutlined']
    CIconPlusOutlined: typeof import('@ant-design/icons-vue')['PlusOutlined']
    CIconQuestionCircleOutlined: typeof import('@ant-design/icons-vue')['QuestionCircleOutlined']
    CIconRobotOutlined: typeof import('@ant-design/icons-vue')['RobotOutlined']
    CIconSaveOutlined: typeof import('@ant-design/icons-vue')['SaveOutlined']
    CIconSmileOutlined: typeof import('@ant-design/icons-vue')['SmileOutlined']
    CIconUploadOutlined: typeof import('@ant-design/icons-vue')['UploadOutlined']
    CIconUserOutlined: typeof import('@ant-design/icons-vue')['UserOutlined']
    CImage: typeof import('ch2-components/lib/image')['Image']
    CInput: typeof import('ch2-components/lib/input')['Input']
    CInputNumber: typeof import('ch2-components/lib/input-number')['InputNumber']
    CInputPassword: typeof import('ch2-components/lib/input-password')['InputPassword']
    CList: typeof import('ch2-components/lib/list')['List']
    CModal: typeof import('ch2-components/lib/modal')['Modal']
    COpenLink: typeof import('ch2-components/lib/open-link')['OpenLink']
    CProForm: typeof import('ch2-components/lib/pro-form')['ProForm']
    CProTable: typeof import('ch2-components/lib/pro-table')['ProTable']
    CRangePicker: typeof import('ch2-components/lib/range-picker')['RangePicker']
    CSearchForm: typeof import('ch2-components/lib/search-form')['SearchForm']
    CSelect: typeof import('ch2-components/lib/select')['Select']
    CStandard: typeof import('ch2-components/lib/standard')['Standard']
    CTable: typeof import('ch2-components/lib/table')['Table']
    CTextarea: typeof import('ch2-components/lib/input')['Textarea']
    CTruncatedText: typeof import('ch2-components/lib/truncated-text')['TruncatedText']
    Editor: typeof import('./components/Editor.vue')['default']
    ExcelImport: typeof import('./components/ExcelImport.vue')['default']
    FileManager: typeof import('./components/FileManager/FileManager.vue')['default']
    GroupSelect: typeof import('./components/GroupSelect.vue')['default']
    InputPercentage: typeof import('./components/InputPercentage.vue')['default']
    Loading: typeof import('./components/Loading.vue')['default']
    PushRecords: typeof import('./components/PushRecords.vue')['default']
    PushReminder: typeof import('./components/PushReminder.vue')['default']
    PushUsers: typeof import('./components/PushUsers.vue')['default']
    QRActivationLog: typeof import('./components/QRActivationLog.vue')['default']
    RisklevelTag: typeof import('./components/RisklevelTag.vue')['default']
    RolesSelect: typeof import('./components/RolesSelect.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TagCheckboxGroup: typeof import('./components/TagCheckboxGroup.vue')['default']
    TagSelect: typeof import('./components/TagSelect.vue')['default']
    TypeSelect: typeof import('./components/TypeSelect.vue')['default']
    Upload: typeof import('./components/FileManager/Upload.vue')['default']
    UserList: typeof import('./components/UserList.vue')['default']
    VerificationQR: typeof import('./components/VerificationQR.vue')['default']
    ViewFile: typeof import('./components/ViewFile.vue')['default']
  }
}
