import { DeptPushChannel } from "./DeptPushChannel";
import { PushStatus } from "./PushStatus";
import { DeptPushLog } from "./DeptPushLog";
/**发送渠道表*/
export class DeptPushChannelLog {
  createdTime: Dayjs = dayjs();
  /**发送的标题/摘要（邮件、微信）*/
  abstract?: string | null | undefined = null;
  /**推送的具体内容，使用模板拼接*/
  text?: string | null | undefined = null;
  /**附件*/
  attachments?: Array<GUID> = [];
  /**推送渠道*/
  channel: DeptPushChannel = 0;
  status: PushStatus = 0;
  /**响应信息（错误信息）*/
  responseMessage?: string | null | undefined = null;
  deptPushLogId: GUID = "00000000-0000-0000-0000-000000000000";
  /**合作单位推送记录*/
  deptPushLog?: DeptPushLog | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
