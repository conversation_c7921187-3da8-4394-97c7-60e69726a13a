<template>
  <a-drawer
    v-model:open="detailsRef.visible" :title="detailsRef.title" placement="right" destroy-on-close
    :closable="false" width="30%"
  >
    <template #extra>
      <a-button style="margin-right: 8px" @click="detailsRef.visible = false">取消</a-button>
      <a-button type="primary" @click="onSave">确定</a-button>
    </template>
    <template v-if="detailsRef.type === '查看'">
      <a-descriptions bordered :column="{ xxl: 1, xl: 1, lg: 1, md: 1, sm: 1, xs: 1 }">
        <a-descriptions-item label="登录账号" :span="1">{{ current.userName }}</a-descriptions-item>
        <a-descriptions-item label="名称" :span="1">{{ current.name }}</a-descriptions-item>
        <a-descriptions-item label="手机号码" :span="1">{{ current.phoneNumber }}</a-descriptions-item>
        <a-descriptions-item label="邮箱" :span="1">{{ current.email }}</a-descriptions-item>
        <a-descriptions-item label="用户角色" :span="1">
          <div v-for="(item, index) in current.roles" :key="index">
            <a-tag color="#87d068">{{ item.name }}</a-tag>
          </div>
        </a-descriptions-item>
      </a-descriptions>
      <a-descriptions-item label="密码更新时限" :span="1">{{ current.expiration }}</a-descriptions-item>
    </template>

    <template v-if="detailsRef.type === '编辑'">
      <a-descriptions bordered :column="{ xxl: 1, xl: 1, lg: 1, md: 1, sm: 1, xs: 1 }">
        <a-descriptions-item label="登录账号" :span="1">
          <a-input v-model:value="editModel.userName" placeholder="请输入用户名" />
        </a-descriptions-item>
        <a-descriptions-item label="名 称" :span="1">
          <a-input v-model:value="editModel.name" placeholder="请输入名称" />
        </a-descriptions-item>
        <a-descriptions-item label="上级单位、部门">
          <C2TreeSelect v-model:value="editModel.departmentId!" class="w-full" :api="() => api.DepartmentManage.GetAllDepartmentsAsync({ isCooperate: true })" :show-all-levels="true" />
        </a-descriptions-item>
        <a-descriptions-item label="号 码" :span="1">
          <a-input v-model:value="editModel.phoneNumber" placeholder="请输入手机号码" />
        </a-descriptions-item>
        <a-descriptions-item label="邮 箱" :span="1">
          <a-input v-model:value="editModel.email" placeholder="请输入邮箱" />
        </a-descriptions-item>
        <a-descriptions-item label="密码更新时限" :span="1">
          <a-date-picker show-time placeholder="请选择日期" style="width: 100%;" @change="dateOnChange" />
        </a-descriptions-item>
      </a-descriptions>
    </template>

    <template v-if="detailsRef.type === '新增'">
      <a-descriptions bordered :column="{ xxl: 1, xl: 1, lg: 1, md: 1, sm: 1, xs: 1 }">
        <a-descriptions-item label="登录账号" :span="1">
          <a-input v-model:value="createModel.userName" placeholder="请输入用户名" />
        </a-descriptions-item>
        <a-descriptions-item label="登录密码" :span="1">
          <a-input v-model:value="createModel.password" placeholder="请输入登录密码" />
          <a-progress
            style="width: 100%" :percent="currentStep" :steps="step" :stroke-color="stepColor"
            :format="strength"
          />
        </a-descriptions-item>
        <a-descriptions-item label="名 称" :span="1">
          <a-input v-model:value="createModel.name" placeholder="请输入名称" />
        </a-descriptions-item>
        <a-descriptions-item label="上级单位、部门">
          <C2TreeSelect v-model:value="createModel.departmentId!" :api="api.DepartmentManage.GetAllDepartmentsAsync" :show-all-levels="true" />
        </a-descriptions-item>
        <a-descriptions-item label="号 码" :span="1">
          <a-input v-model:value="createModel.phoneNumber" placeholder="请输入手机号码" />
        </a-descriptions-item>
        <a-descriptions-item label="邮 箱" :span="1">
          <a-input v-model:value="createModel.email" placeholder="请输入邮箱" />
        </a-descriptions-item>
        <a-descriptions-item label="密码更新时限" :span="1">
          <a-date-picker show-time placeholder="请选择日期" style="width: 100%;" @change="dateOnChange" />
        </a-descriptions-item>
      </a-descriptions>
    </template>
  </a-drawer>
</template>

<script setup lang='tsx'>
import * as api from '@/api'
import * as models from '@/api/models'
import { message } from 'ant-design-vue'
import { ref } from 'vue'

const detailsRef = ref({
  visible: false,
  title: '',
  currentId: '0',
  deptId: Guid.empty,
  type: '',
  callback: () => { },
})

const current = ref<models.UserViewModel>(new models.UserViewModel())
const editModel = ref<models.UserEditModel>(new models.UserEditModel())
const createModel = ref<models.UserCreateModel>(new models.UserCreateModel())

// 步骤条组件总步数
const step = ref(5)

// 当前进度
const currentStep = ref<number>(0)

// 根据进度步骤条分别显示不同颜色
const stepColor = computed(() => (currentStep.value <= 40 ? 'red' : currentStep.value <= 80 ? '#f75300' : '#29f700'))
// 步骤条文本数组
const strengthArr = ref(['非常弱', '弱', '中等', '强', '非常强'])
// 步骤条当前文本
function strength() {
  return strengthArr.value[currentStep.value / 20 - 1]
}

/**
 * @function 监听事件
 * @description 监听新密码变化控制进度条
 */
watch(
  () => createModel.value.password,
  (newValue) => {
    const check = chackPasswordStrength(newValue?.toString())
    if (newValue && newValue.length > 0)
      currentStep.value = (check + 1) * 20
    else
      currentStep.value = 0
  },
)

/**
 * @function 检查密码复杂度
 * @param password 新密码
 * @description 判断密码强度，分5个等级
 */
function chackPasswordStrength(password: string | any) {
  const patterns = [/\d/, /[a-z]/, /[A-Z]/, /[^a-z\d]/i]
  const count = patterns.reduce((acc, pattern) => acc + Number(pattern.test(password)), 0)
  const len = password.length
  return len < 8 ? 0 : len >= 16 && count >= 3 ? 4 : count < 3 ? (len < 12 ? 1 : 3) : len < 12 ? 2 : 3
}

/**
 * 日期变化
 */
function dateOnChange(value: Dayjs) {
  editModel.value.expiration = value
}

async function onSave() {
  if (detailsRef.value.type === '编辑') {
    await api.UserManage.EditUserModel_PostAsync(editModel.value).then((res) => {
      if (res) {
        message.success('保存成功')
        detailsRef.value.visible = false
        detailsRef.value.callback()
      }
    })
  }
  else if (detailsRef.value.type === '新增') {
    await api.UserManage.CreateUser_PostAsync(createModel.value).then((res) => {
      if (res) {
        message.success('创建成功')
        detailsRef.value.callback()
        detailsRef.value.visible = false
      }
    })
  }
}

/**
 *  父组件传值
 * @param params 参数
 */
async function acceptParams(params: typeof detailsRef.value): Promise<void> {
  detailsRef.value = params
  detailsRef.value.visible = params.visible
  if (params.currentId) {
    getData(params.currentId)
  }
  else {
    createModel.value = new models.UserCreateModel()
    createModel.value.departmentId = detailsRef.value.deptId
  }
}

/**
 * 获取详情
 */
async function getData(id: string) {
  await api.UserManage.GetUserViewModelAsync({ userId: id }).then((res) => {
    if (res) {
      current.value = res
      editModel.value = res
    }
  })
}

defineExpose({
  acceptParams,
})
</script>

<style scoped lang='less'>
:deep(.ant-descriptions-item-label) {
  background-color: #fafafa;
  width: 150px;
}

:deep(.ant-progress-steps-item) {
  min-width: 16%;
}
</style>
