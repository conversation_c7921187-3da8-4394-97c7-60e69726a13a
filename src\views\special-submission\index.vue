<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2025-02-25 10:12:06
 * @LastEditors: 景 彡
-->
<template>
  <div class="ch2-scroll-container scrollbar-thumb-gray-400 flex gap-16px overflow-auto bg-bg-container p-16px">
    <div
      v-for="item in specialSubjectData" :key="item.id"
      class="group relative box-border cursor-pointer border-(3px bg-container solid)"
      :class="{ 'border-primary': item.id === currentTopicData.id }"
      @click="topicClick(item)"
    >
      <div class="relative h-200px w-300px filter-blur-2px">
        <div v-if="timeJudgment(item.start, item.end) === TimeJudgmentEnum.已过期" class="absolute h-200px w-300px bg-[rgba(0,0,0,0.2)]" />
        <img v-if="item.img" class="h-100% w-100%" :src="joinFilePathById(item.img)" alt="">
      </div>
      <template v-if="timeJudgment(item.start, item.end) === TimeJudgmentEnum.跟踪中">
        <div class="absolute top-50% w-100% translate-y--50% rounded-r-xl bg-[linear-gradient(90deg,rgba(9,179,136,0.8)_0%,rgba(10,204,156,0)_100%)] p-8px text-center text-16px text-4 c-#fff font-bold text-shadow text-shadow-[1px_1px_0_#8A8080,-1px_-1px_0_#8A8080,-1px_-1px_0_#8A8080,1px_0_0_#8A8080,-1px_0_0_#8A8080,0_1px_0_#8A8080,0_-1px_0_#8A8080]">{{ item.title }}</div>
        <div class="absolute right-0px top-20px rotate-46deg c-[rgba(10,255,194,1)] font-bold tracking-3px">跟踪中</div>
      </template>

      <template v-else-if="timeJudgment(item.start, item.end) === TimeJudgmentEnum.未开始">
        <div class="absolute top-50% w-100% translate-y--50% rounded-r-xl bg-[linear-gradient(90deg,rgba(83,161,233,1)_0%,rgba(87,183,233,0)_100%)] p-8px text-center text-16px text-4 c-#fff font-bold text-shadow-[1px_1px_0_#8A8080,-1px_-1px_0_#8A8080,-1px_-1px_0_#8A8080,1px_0_0_#8A8080,-1px_0_0_#8A8080,0_1px_0_#8A8080,0_-1px_0_#8A8080]">{{ item.title }}</div>
        <div class="absolute right-0px top-20px rotate-46deg c-[rgba(255,255,255,1)] font-bold tracking-3px">未开始</div>
      </template>
      <template v-else-if="timeJudgment(item.start, item.end) === TimeJudgmentEnum.已过期">
        <div class="absolute top-50% w-100% translate-y--50% rounded-r-xl bg-[linear-gradient(90deg,rgba(10,0,0,0.5)_0%,rgba(255,255,255,0)_100%)] p-8px text-center text-16px text-4 c-#fff font-bold text-shadow text-shadow-[1px_1px_0_#8A8080,-1px_-1px_0_#8A8080,-1px_-1px_0_#8A8080,1px_0_0_#8A8080,-1px_0_0_#8A8080,0_1px_0_#8A8080,0_-1px_0_#8A8080]">{{ item.title }}</div>
        <div class="absolute right-0px top-20px rotate-46deg c-[rgba(194,194,194,1)] font-bold tracking-3px">已过期</div>
      </template>

      <div v-if="$auth([_Role.舆情监测人员, _Role.舆情监测中心领导])" class="absolute bottom-4 right-2 hidden group-hover:block">
        <c-icon-edit-outlined class="ml-16px text-6 c-#fff font-bold" @click="onEdit(item)" />
        <c-icon-delete-outlined class="ml-16px text-6 c-error font-bold" @click="onDel(item)" />
      </div>
    </div>
  </div>

  <div class="mt-16px bg-bg-container">
    <div class="flex items-center justify-between border-1px border-b-border border-b-solid px-4 py-2">
      <div v-show="currentTopicData.id" class="text-4 c-text">
        {{ `${currentTopicData.title}（${dateTime(currentTopicData.start, 'YYYY-MM-DD')} -- ${dateTime(currentTopicData.end, 'YYYY-MM-DD')}）` }}
        <template v-if="$auth([_Role.舆情监测中心领导, _Role.舆情监测人员])"><a-tag v-for="dept in currentTopicData.departments" :key="dept.id" color="orange">{{ dept.name }}</a-tag></template>
      </div>
      <div v-if="$auth([_Role.舆情监测中心领导, _Role.舆情监测人员])"><a-button type="primary" :icon="h(PlusOutlined)" @click="onEdit(new PublicOpinionTopicEditModel())">新增专题</a-button></div>
    </div>
    <div class="p-16px">
      <div class="bg-fill-tertiary p-16px indent-2rem c-text-secondary line-height-24px">{{ currentTopicData.summary }}</div>
    </div>
  </div>

  <div class="mt-16px bg-bg-container c-text">
    <div class="flex justify-between border-1px border-b-border border-b-solid p-4 text-4">
      <div>舆情事件列表</div>
      <a-button v-if="$auth([_Role.舆情监测中心领导, _Role.舆情监测人员])" type="primary" :icon="h(PlusOutlined)" @click="publicOpinionHook.add()">新增舆情</a-button>
    </div>
    <div class="px-4 pb4">
      <c-pro-table
        ref="proTableRef"
        :columns="columns"
        :api="api.OpinionManage.GetListAsync"
        row-key="id"
        :get-params="{ topicId: currentTopicData.id as any }"
        :show-search="false"
        :show-tool-btn="false"
        serial-number
        :operation="$auth([_Role.舆情监测中心领导, _Role.舆情监测人员])"
        :operation-config="{ width: '60px' }"
      >
        <template v-if="$auth([_Role.舆情监测中心领导, _Role.舆情监测人员])" #operation="{ record }">
          <a-popconfirm title="是否将该舆情移出该专题" @confirm="delOption(record.id)">
            <a-button type="link" danger>移出</a-button>
          </a-popconfirm>
        </template>
      </c-pro-table>
    </div>
  </div>

  <a-drawer v-model:open="open" destroy-on-close title="添加/编辑专题" width="860px" @close="open = false">
    <c-pro-form v-model:value="form" :fields="fields" :descriptions="{ column: 1, bordered: true }" @finish="onSave">
      <template #img>
        <div v-if="form.img" class="coverBox size-140px overflow-hidden">
          <c-image :src="joinFilePathById(form.img)" alt="avatar" :preview="true" :del-ico="true" style="height: 140px; width:140px ; object-fit:cover" @del-image="() => form.img = ''" />
        </div>
        <a-button v-else type="dashed" block style="width: 100px; height: 100px" @click="avatarUpload">
          <template #icon>
            <c-icon-plus-outlined />
          </template>
          上传
        </a-button>
      </template>
      <template #deptIds>
        <C2TreeSelect
          v-model:value="form.deptIds!" :api="api.DepartmentManage.GetAllDepartmentsAsync"
          tree-checkable
        />
      </template>
      <template #footer>
        <a-descriptions-item label="操作">
          <a-button type="primary" html-type="submit">
            保存
          </a-button>
        </a-descriptions-item>
      </template>
    </c-pro-form>
  </a-drawer>

  <OpinionEditForm v-model:open="publicOpinionHook.open.value" v-model="publicOpinionHook.formData.value" :read-only="publicOpinionHook.readOnly.value" />
</template>

<script lang='ts' setup>
import type { PublicOpinionTopic, PublicOpinionViewModel } from '@/api/models'
import type { FormField } from 'ch2-components/lib/pro-form/types'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import type { Dayjs } from 'dayjs'
import * as api from '@/api'
import { FileType, PublicOpinionEditModel, PublicOpinionTopicEditModel, PublicOpinionTopicViewModel } from '@/api/models'
import { useFileMangerModal } from '@/hooks/useFileMangerModal'
import { $auth } from '@/permission'
import { _Role } from '@/permission/RoleName'
import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import modal from 'ant-design-vue/es/modal'
import TruncatedText from 'ch2-components/lib/truncated-text/src/TruncatedText.vue'
import dayjs from 'dayjs' // 引入 dayjs
import isBetween from 'dayjs/plugin/isBetween'
import { useRoute, useRouter } from 'vue-router'
import OpinionEditForm from '../public-opinion/components/EditForm.vue'

dayjs.extend(isBetween)

definePage({
  meta: {
    title: '专题报送',
    icon: 'CarryOutOutlined',
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '专题报送',
        local: true,
        icon: 'CarryOutOutlined',
        order: 8,
      },
    },
  },
})

enum TimeJudgmentEnum {
  未开始 = 0,
  跟踪中 = 1,
  已过期 = 2,
}

const router = useRouter()

const route = useRoute()

watch(() => route.query, () => {
  getData()
})

const timeJudgment = computed(() => (start: Dayjs | undefined, end: Dayjs | undefined) => {
  const now = dayjs()
  const startDate = dayjs(start)
  const endDate = dayjs(end)

  if (now.isBetween(startDate, endDate, null, '[]')) {
    return TimeJudgmentEnum.跟踪中
  }
  else if (now.isBefore(startDate)) {
    return TimeJudgmentEnum.未开始
  }
  else if (now.isAfter(endDate)) {
    return TimeJudgmentEnum.已过期
  }
})

const { open, form, fields, onSave, onEdit, avatarUpload, onDel } = useAddHook()

const publicOpinionHook = usePublicOpinion()

const columns = ref<ColumnProps<PublicOpinionViewModel>[]>([
  {
    dataIndex: 'summary',
    title: '舆情主题',
    key: 'summary',
    bodyCell: ({ record }) => {
      return h('a', { onclick: () => {
        publicOpinionHook.readOnly.value = true
        publicOpinionHook.formData.value = record as PublicOpinionEditModel
        publicOpinionHook.open.value = true
      } }, [h(TruncatedText, { text: record.summary!, maxLines: 2, type: 'tooltip' })])
    },
  },
  {
    dataIndex: 'source',
    title: '来源',
    key: 'source',
  },
  {
    dataIndex: 'category',
    title: '类别',
    key: 'category',
  },
  {
    dataIndex: 'event',
    title: '关联事件',
    key: 'event',
    bodyCell: ({ record }) => {
      return h('a', { onclick: () => {
        if (record.event)
          router.push({ path: '/event-library/detail', query: { id: record.event.id } })
      } }, { default: () => record.event?.summary })
    },
  },
  {
    dataIndex: 'serious',
    title: '重大舆情',
    key: 'serious',
    align: 'center',
    bodyCell: ({ record }) => {
      return h('span', null, { default: () => record.serious ? '是' : '否' })
    },
  },
  {
    dataIndex: ['createdUser', 'userName'],
    title: '上报人',
    key: 'createdUser',
  },
  {
    dataIndex: 'createdAt',
    title: '上报时间',
    key: 'createdAt',
    dateFormat: 'YYYY-MM-DD HH:mm',
  },
])

const specialSubjectData = ref<PublicOpinionTopic[]>([])

const currentTopicData = ref(new PublicOpinionTopicViewModel())

const params = ref({
  title: '',
  summary: '',
  limit: 999,
  offset: 0,
})

const proTableRef = useTemplateRef('proTableRef')

async function getDataById(id: GUID) {
  currentTopicData.value = await api.OpinionTopicManage.GetAsync({ id })
  await nextTick()
  proTableRef.value?.search()
}

async function getData() {
  try {
    const res = await api.OpinionTopicManage.GetListAsync(params.value)
    specialSubjectData.value = res.items || []
    console.log('specialSubjectData.value', specialSubjectData.value[0])

    if (specialSubjectData.value.length > 0) {
      if (route.query?.id) {
        getDataById(route.query?.id)
      }
      else {
        getDataById(specialSubjectData.value[0].id)
      }
    }

    await nextTick()
    if (currentTopicData.value.id)
      proTableRef.value?.search()
  }
  catch (error: any) {
    message.error(error.message)
  }
}

async function topicClick(res: PublicOpinionTopic) {
  await getDataById(res.id)
}

function delOption(id: string) {
  api.OpinionTopicManage.RemoveOpinion2Topic_GetAsync({ opinionId: id }).then(() => {
    proTableRef.value?.search()
    message.success('移除成功')
  })
}

function useAddHook() {
  const open = ref(false)

  const form = ref(new PublicOpinionTopicEditModel())

  const fields = ref<FormField<PublicOpinionTopicEditModel>[]>([
    {
      label: '标题',
      prop: 'title',
      el: 'input',
      formItem: {
        rules: [{ required: true, message: '标题必填!' }],
      },
      attrs: {},
    },
    {
      label: '专题概况',
      prop: 'summary',
      el: 'textarea',
      formItem: {
        rules: [{ required: true, message: '专题概况必填!' }],
      },
      attrs: { rows: 4, showCount: true, maxlength: 100 },
    },
    {
      label: '图片',
      prop: 'img',
      el: 'input',
      formItem: {
        rules: [{ required: true, message: '图片必填!' }],
      },
      attrs: {},
    },
    {
      label: '监控单位',
      prop: 'deptIds',
      el: 'input',
      formItem: {
        rules: [{ required: true, message: '监控单位必填!' }],
      },
      attrs: {},
    },
    {
      label: '开始时间',
      prop: 'start',
      el: 'date-picker',
      formItem: {
        rules: [{ required: true, message: '开始时间必填!' }],
      },
      attrs: {
        style: { width: '100%' },
      },
    },
    {
      label: '结束时间',
      prop: 'end',
      el: 'date-picker',
      attrs: { style: { width: '100%' } },
    },
  ])

  async function onSave() {
    form.value.start = dayjs(form.value.start).startOf('day').format()
    form.value.end = dayjs(form.value.end).endOf('day').format()
    // form.value.end = dayjs(form.value.end).format('YYYY-MM-DD 00:00')
    const temp = Guid.isNotNull(form.value.id) ? () => api.OpinionTopicManage.Update_PostAsync(form.value) : () => api.OpinionTopicManage.Create_PostAsync(form.value)
    try {
      await temp()
      getData()
      message.success('保存成功')
      open.value = false
    }
    catch (error: any) {
      message.error(error.message)
    }
  }

  function onEdit(record: PublicOpinionTopicEditModel) {
    form.value = record
    open.value = true
  }

  function onDel(record: PublicOpinionTopicEditModel) {
    modal.confirm({
      title: `确定删除【${record.title}】专题吗`,
      icon: h(ExclamationCircleOutlined),
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      async onOk() {
        try {
          await api.OpinionTopicManage.Delete_PostAsync({ id: record.id! })
          getData()
          message.success('删除成功')
        }
        catch (error: any) {
          message.error(`删除失败：${error.message}`)
        }
      },
    })
  }

  function avatarUpload() {
    useFileMangerModal((files) => {
      form.value.img = files[0]?.id
    }, { multiple: false, immediateReturn: true, menu: [FileType.图片] })
  }

  return { open, form, fields, onSave, onEdit, avatarUpload, onDel }
}

function usePublicOpinion() {
  const open = ref(false)

  const readOnly = ref(false)

  const formData = ref(new PublicOpinionEditModel())

  function onClose() {
    formData.value = new PublicOpinionEditModel()
    open.value = false
  }
  const add = () => {
    readOnly.value = false
    open.value = true
    formData.value = {
      ...new PublicOpinionEditModel(),
    }
  }
  return { open, onClose, add, formData, onSave, readOnly }
}

onMounted(() => {
  getData()
})
</script>

<style scoped>
.ch2-scroll-container::-webkit-scrollbar {
  height: 6px; /* 滚动条高度 */
}
</style>
