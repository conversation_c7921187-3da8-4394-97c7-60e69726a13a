<template>
  <c-modal v-model:open="open" title="合作单位推送提醒" width="80%" destroy-on-close mask-closable>
    <div class="flex">
      <div class="mr-4 w-520px border-r border-border border-r-solid pr-4 space-y-4">
        <slot name="left" />
        {{ currentDepartmentId }}
        <a-divider dashed />

        <!-- 推送用户组件 -->
        <PushUsers
          v-model:current-dept-id="currentDeptId"
          v-model:force="sendForm.force"
          @data-change="handleUserDataChange"
        />
      </div>

      <!-- 推送记录组件 -->
      <div class="flex-1">
        <PushRecords
          :table-data="tableData"
          :loading="tableLoding"
        />
      </div>
    </div>
    <template #footer>
      <a-button key="back" @click="open = false">稍后推送</a-button>
      <a-button key="submit" type="primary" :loading="loading" @click="onSend">立即推送</a-button>
    </template>
  </c-modal>
</template>

<script lang="ts" setup>
import type { DeptMainPush, DeptPushChannel, DeptPushType, PushTarget, UserViewModel } from '@/api/models'
import * as api from '@/api'

import { message } from 'ant-design-vue'
import PushRecords from './PushRecords.vue'
import PushUsers from './PushUsers.vue'

const props = defineProps<{
  currentDepartmentId?: GUID
  entityId?: GUID
  entityType: DeptPushType
}>()

const open = defineModel<boolean>('open', {
  required: true,
  default: false,
})

const loading = ref(false)

const sendForm = ref({
  abstractText: '',
  pushUser: Guid.empty,
  publicOpinionId: props.entityId,
  force: false,
})

const channelMap = ref<Record<string, DeptPushChannel[]>>({})
const currentDeptId = ref<GUID>(Guid.empty)
const userData = ref<UserViewModel[]>([])
const tableData = ref<DeptMainPush[]>([])
const tableLoding = ref(false)

// 处理子组件数据变化
function handleUserDataChange(data: { userData: UserViewModel[], channelMap: Record<string, DeptPushChannel[]> }) {
  userData.value = data.userData
  channelMap.value = data.channelMap
}

function getSubmitData(): PushTarget[] {
  return userData.value.map(user => ({
    pushUserId: user.id as string,
    channels: channelMap.value[user.id!] || [],
  }))
}

async function getUserPushStatus() {
  tableLoding.value = true
  try {
    tableData.value = await api.DeptPushLogs.GetUserPushStatusAsync({
      departmentId: currentDeptId.value,
      entityId: props.entityId,
      entityType: props.entityType,
    })
    tableLoding.value = false
  }
  catch (error) {
    console.log('%c [ error ]-195', 'font-size:13px; background:pink; color:#bf2c9f;', error)
    tableLoding.value = false
  }
}

async function onSend() {
  loading.value = true
  const submitData = getSubmitData()

  try {
    await api.DeptPushLogs.Send_PostAsync({
      entityId: props.entityId,
      entityType: props.entityType,
      force: sendForm.value.force,
    }, submitData)
    message.success('推送成功')
    loading.value = false
    getUserPushStatus()
  }
  catch (error: any) {
    console.log(error)
    loading.value = false
  }
}

watch(
  () => open.value,
  (newVal) => {
    console.log('%c [ props.currentDepartmentId ]-223', 'font-size:13px; background:pink; color:#bf2c9f;', props.currentDepartmentId)
    if (newVal && props.currentDepartmentId) {
      currentDeptId.value = props.currentDepartmentId
      getUserPushStatus()
    }
  },
)
</script>
