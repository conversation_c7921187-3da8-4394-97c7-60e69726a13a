<template>
  <a-modal v-model:open="open" title="合作单位推送提醒" :width="hasRecords ? '80%' : '800px'" destroy-on-close mask-closable>
    <div class="flex">
      <div class="mr-4 border-r border-border border-r-solid pr-4 space-y-4" :class="hasRecords ? 'w-520px' : 'w-800px'">
        <slot name="left" />
        {{ currentDepartmentId }}
        <a-divider dashed />

        <!-- 推送用户组件 -->
        <PushUsers
          v-model:current-dept-id="currentDeptId"
          v-model:channel-map="channelMap"
          v-model:force="sendForm.force"
          :user-data="userData"
          :spinning="spinning"
          :is-edit="isEdit"
          @dept-change="deptChange"
          @edit-toggle="isEdit = !isEdit"
        />
      </div>

      <!-- 推送记录组件 -->
      <div v-if="hasRecords" class="flex-1">
        <PushRecords
          :table-data="tableData"
          :loading="tableLoding"
        />
      </div>
    </div>
    <template #footer>
      <a-button key="back" @click="open = false">稍后推送</a-button>
      <a-button key="submit" type="primary" :loading="loading" @click="onSend">立即推送</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import type { DeptMainPush, DeptPushChannel, DeptPushType, PushTarget, UserViewModel } from '@/api/models'
import * as api from '@/api'

import { message } from 'ant-design-vue'
import PushRecords from './PushRecords.vue'
import PushUsers from './PushUsers.vue'

const props = defineProps<{
  currentDepartmentId?: GUID
  entityId?: GUID
  entityType: DeptPushType
}>()

const open = defineModel<boolean>('open', {
  required: true,
  default: false,
})

const loading = ref(false)

const sendForm = ref({
  abstractText: '',
  pushUser: Guid.empty,
  publicOpinionId: props.entityId,
  force: false,
})

const channelMap = ref<Record<string, DeptPushChannel[]>>({})
const currentDeptId = ref<GUID>(Guid.empty)
const isEdit = ref(false)
const userData = ref<UserViewModel[]>([])
const spinning = ref(false)
const tableData = ref<DeptMainPush[]>([])
const tableLoding = ref(false)

// 计算属性：是否有推送记录
const hasRecords = computed(() => tableData.value.length > 0)

async function getUserData() {
  spinning.value = true
  try {
    userData.value = await api.DeptPushLogs.GetPushUserAsync({ departmentId: currentDeptId.value })
    channelMap.value = {}
    userData.value.forEach((e) => {
      channelMap.value[e.id!] = e.deptPushChannels
    })
    spinning.value = false
  }
  catch (error) {
    console.log('%c [ error ]-154', 'font-size:13px; background:pink; color:#bf2c9f;', error)
    spinning.value = false
  }
}

function deptChange() {
  getUserData()
}

function getSubmitData(): PushTarget[] {
  return userData.value.map(user => ({
    pushUserId: user.id as string,
    channels: channelMap.value[user.id!] || [],
  }))
}

async function getUserPushStatus() {
  tableLoding.value = true
  try {
    tableData.value = await api.DeptPushLogs.GetUserPushStatusAsync({
      departmentId: currentDeptId.value,
      entityId: props.entityId,
      entityType: props.entityType,
    })
    tableLoding.value = false
  }
  catch (error) {
    console.log('%c [ error ]-195', 'font-size:13px; background:pink; color:#bf2c9f;', error)
    tableLoding.value = false
  }
}

async function onSend() {
  loading.value = true
  const submitData = getSubmitData()

  try {
    await api.DeptPushLogs.Send_PostAsync({
      entityId: props.entityId,
      entityType: props.entityType,
      force: sendForm.value.force,
    }, submitData)
    message.success('推送成功')
    loading.value = false
    getUserPushStatus()
  }
  catch (error: any) {
    console.log(error)
    loading.value = false
  }
}

watch(
  () => open.value,
  (newVal) => {
    console.log('%c [ props.currentDepartmentId ]-223', 'font-size:13px; background:pink; color:#bf2c9f;', props.currentDepartmentId)
    if (newVal && props.currentDepartmentId) {
      currentDeptId.value = props.currentDepartmentId
      getUserData()
      getUserPushStatus()
    }
  },
)
</script>
