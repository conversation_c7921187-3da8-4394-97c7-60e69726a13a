import { Department } from "./Department";
import { ReadingStatus } from "./ReadingStatus";
import { User } from "./User";
/**关联单位（管理给合作单位查询）*/
export class CarbonCopyView {
  /**是否推送提醒给单位*/
  isPushDept: boolean = false;
  /**抄送备注*/
  context?: string | null | undefined = null;
  /**抄送时间*/
  time: Dayjs = dayjs();
  /**抄送单位*/
  departmentId?: GUID = null;
  eventId: GUID = "00000000-0000-0000-0000-000000000000";
  /**部门表*/
  department?: Department | null | undefined = null;
  /**要求单位 阅读状态 (只用到 已读、未读)*/
  auditStatus?: ReadingStatus | null | undefined = null;
  auditAt?: Dayjs | null | undefined = null;
  auditBy?: GUID = null;
  /**用户*/
  auditUser?: User | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**创建者*/
  createdId?: GUID = null;
  /**创建者*/
  createdBy?: User | null | undefined = null;
}
