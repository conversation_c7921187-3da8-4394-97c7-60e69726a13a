<template>
  <c-modal
    v-model:open="open"
    title="新增推送"
    width="80%"
    full-modal
    :confirm-loading="confirmLoading"
    @ok="onSubmit"
    @cancel="onCancel"
  >
    <div class="mb-4">
      <div class="mb-2 text-base font-medium">推送单位：{{ deptRecord?.deptName }}</div>
    </div>

    <a-tabs v-model:active-key="activeTab">
      <a-tab-pane
        :key="DeptPushType.舆情日报"
        tab="舆情日报"
      >
        <PushUsers
          v-model:current-dept-id="deptRecord.deptId"
          v-model:force="sendForm.force"
          @data-change="handleUserDataChange"
        />
        <PushRecords
          :current-dept-id="currentDeptId"
          :entity-id="entityId"
          :entity-type="entityType"
        />
        <div>
          <a-button type="primary" ghost>日报推送</a-button>
          <a-button type="primary" ghost class="ml-4" @click="useExport.exportDailyNewspaper">导出日报</a-button>
        </div>
      </a-tab-pane>
    </a-tabs>
  </c-modal>
</template>

<script lang="ts" setup>
import * as api from '@/api'
import { DeptPushType } from '@/api/models'

import { message } from 'ant-design-vue'

interface DeptModel {
  deptName: string
  deptId: GUID
}

const props = defineProps<{
  deptRecord?: DeptModel
}>()

const emit = defineEmits<{
  (e: 'success'): void
}>()

const useExport = useExportHook()

const open = defineModel<boolean>('open', {
  required: true,
  default: false,
})

const confirmLoading = ref(false)
const activeTab = ref<DeptPushType>(DeptPushType.疑似舆情)

// 表单引用
const formRefs = ref<Record<DeptPushType, any>>({})

// function onTabChange(key: string) {
//   activeTab.value = Number(key) as DeptPushType
// }

async function onSubmit() {
  if (!props.deptRecord) {
    message.error('请选择推送单位')
    return
  }

  const currentForm = formRefs.value[activeTab.value]
  if (!currentForm) {
    message.error('表单未准备就绪')
    return
  }

  try {
    confirmLoading.value = true
    await currentForm.submit()
    message.success('推送成功')
    emit('success')
    open.value = false
  }
  catch (error: any) {
    console.error('推送失败:', error)
    message.error(error.message || '推送失败')
  }
  finally {
    confirmLoading.value = false
  }
}

function onCancel() {
  open.value = false
}

// 监听对话框打开状态，重置表单
watch(() => open.value, (newVal) => {
  if (newVal) {
    activeTab.value = DeptPushType.疑似舆情
    formRefs.value = {}
  }
})

/** 导出 */
function useExportHook() {
  function exportDailyNewspaper() {
    useDownload(() => api.OpinionManage.ExportDailyNewspaperWord_PostAsync({ }))
  }

  function exportBriefing() {
    useDownload(() => api.OpinionManage.ExportSimpleDailyReport_GetAsync({ }))
  }

  return { exportDailyNewspaper, exportBriefing }
}
</script>

<style scoped lang="less">
:deep(.ant-tabs-content-holder) {
  min-height: 300px;
}
</style>
