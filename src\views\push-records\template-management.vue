<template>
  <c-pro-table
    size="small"
    :row-key="(record) => record.id"
    :columns="columns"
    :data-source="tableData"
    immediate
    operation
    :show-search="false"
  >
    <template #header>
      <a-button type="primary" :icon="h(PlusOutlined)" @click="onAdd">
        新增
      </a-button>
    </template>

    <template #bodyCell="{ record, column }">
      <template v-if="column.dataIndex === 'body'">
        <a @click="onReview(record.body)">点击查看模板</a>
        <!-- <div v-html="record.body" /> -->
      </template>
      <template v-if="column.dataIndex === 'enableTemplate'">
        <div v-if="editRowMap[record.templateName]" class="flex items-center">
          <a-select
            v-model:value="selectMap[record.templateName]"
            :options="options"
            style="width: 100%;"
          />
          <a-button :disabled="!selectMap[record.templateName]" type="primary" ghost size="small" class="ml-2" @click="onSet(record)">保存</a-button>
        </div>
        <div v-else class="flex items-center">
          <span>{{ selectMap[record.templateName] }}</span>
          <a-button type="primary" ghost size="small" class="ml-2" @click="onTemEdit(record)">编辑</a-button>
        </div>
      </template>
    </template>
    <template #operation="{ record }">
      <a-popover title="输入邮箱" trigger="click">
        <template #content>
          <div class="w-280px">
            <a-input v-model:value="params.email" placeholder="请输入邮箱" />
            <div class="mt-2 text-right"> <a-button type="primary" size="small" :loading="sendLoding" @click="onSend(record.templateName!)">发送</a-button></div>
          </div>
        </template>
        <a class="mr-2">测试</a>
      </a-popover>

      <a class="mr-2" @click="onEdit(record)">编辑</a>
      <a-popconfirm
        title="确认删除此模板吗?"
        ok-text="确认"
        cancel-text="取消"
        @confirm="onDel(record.id)"
      >
        <span class="cursor-pointer c-error">删除</span>
      </a-popconfirm>
    </template>
  </c-pro-table>

  <a-drawer
    v-model:open="open"
    title="新增/编辑模板"
    placement="right"
    size="large"
  >
    <c-pro-form
      v-model:value="form"
      :descriptions="{ column: 1, bordered: true, labelStyle: { width: '200px' } }"
      :fields="fields"
      layout="inline"
      @finish="onSave"
    >
      <template #footer>
        <a-descriptions-item label="操作">
          <a-button type="primary" html-type="submit">
            保存
          </a-button>
        </a-descriptions-item>
      </template>
    </c-pro-form>
  </a-drawer>
</template>

<script lang='ts' setup>
import type { EmailTemplate } from '@/api/models'
import type { SelectProps } from 'ant-design-vue'
import type { FormField } from 'ch2-components/types/pro-form/types'
import * as api from '@/api'
import { EmailTemplateEditModel } from '@/api/models'
import { PlusOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'

definePage({
  meta: {
    title: '推送信息模板管理',
    local: true,
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '推送信息模板管理',
        local: true,
        icon: 'UnorderedListOutlined',
        order: 7,
      },
    },
  },
})

const { sendLoding, params, onSend } = useSend()

const columns = ref([
  {
    title: '模板名称',
    dataIndex: 'templateName',
    width: 200,
  },
  { title: '标题', dataIndex: 'subject' },
  { title: '模板内容', dataIndex: 'body' },
  { title: '启用模板', dataIndex: 'enableTemplate', width: 200 },
])

const tableData = ref<EmailTemplate[]>([])

const open = ref(false)

const isEdit = ref(false)

const form = ref(new EmailTemplateEditModel())

const fields = computed<FormField<EmailTemplateEditModel>[]>(() => [
  {
    label: '模板名称',
    prop: 'templateName',
    el: 'input',
    attrs: {
      disabled: isEdit.value,
    },
  },
  {
    label: '标题',
    prop: 'subject',
    el: 'textarea',
    attrs: { rows: 4 },
  },
  {
    label: '模板内容',
    prop: 'body',
    el: 'textarea',
    attrs: { rows: 4 },
  },
])

let templateConfig: Record<string, string> = {}

const options = ref<SelectProps['options']>([])

const selectMap = reactive<Record<string, string>>({})

const editRowMap = reactive<Record<string, boolean>>({})

async function getConfigTem() {
  templateConfig = await api.EmailSetting.GetConfigAsync() as any
  tableData.value.forEach((row) => {
    editRowMap[row.templateName!] = false
    const matchedKey = Object.keys(templateConfig)
      .find(key => templateConfig[key] === row.templateName)
    if (matchedKey) {
      selectMap[row.templateName!] = matchedKey
    }
  })

  options.value = Object.keys(templateConfig).map(key => ({
    label: key,
    value: key,
  }))
}

async function getTableData() {
  tableData.value = await api.EmailSetting.GetEmailTemplatesAsync()
  getConfigTem()
}

function onAdd() {
  isEdit.value = false
  form.value = new EmailTemplateEditModel()
  open.value = true
}

function onEdit(record: EmailTemplateEditModel) {
  isEdit.value = true
  form.value = deepCopy(record)
  open.value = true
}

async function onSave() {
  try {
    await api.EmailSetting.Save_PostAsync(form.value)
    message.success('保存成功')
    open.value = false
    getTableData()
  }
  catch (error) {
    console.log('%c [ error ]-122', 'font-size:13px; background:pink; color:#bf2c9f;', error)
    open.value = false
  }
}

function onTemEdit(record: EmailTemplate) {
  editRowMap[record.templateName!] = true
}

async function onSet(record: EmailTemplate) {
  const selectedKey = selectMap[record.templateName!] // 比如 "日报推送模板"
  if (!selectedKey)
    return

  // 先清除旧占用（保证一对一）
  for (const key in templateConfig) {
    if (templateConfig[key] === record.templateName) {
      templateConfig[key] = ''
      break
    }
  }

  // 绑定新关系
  templateConfig[selectedKey!] = record.templateName!

  await nextTick()

  await api.EmailSetting.SaveConfig_PostAsync(templateConfig)
  message.success('保存成功')
  editRowMap[record.templateName!] = false
}

async function onDel(id: GUID) {
  await api.EmailSetting.DeleteById_PostAsync({ id })
  getTableData()
  message.success('删除成功')
}

onMounted(() => {
  getTableData()
})

function useSend() {
  const sendLoding = ref(false)

  const params = ref({
    email: '',
    templateName: '',
  })
  async function onSend(name: string) {
    sendLoding.value = true
    params.value.templateName = name
    try {
      await api.EmailSetting.TestSendEmail_PostAsync(params.value, {})
      message.success('发送成功')
      sendLoding.value = false
    }
    catch (error) {
      console.log('%c [ error ]-245', 'font-size:13px; background:pink; color:#bf2c9f;', error)
      sendLoding.value = false
    }
  }
  return { sendLoding, params, onSend }
}

function onReview(body: string) {
  Modal.info({
    title: '模板查看',
    width: 800,
    content: h('div', { innerHTML: body }),
  })
}
</script>

<style scoped>

</style>
