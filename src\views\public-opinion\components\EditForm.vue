<template>
  <c-modal v-model:open="open" :title="readOnly ? '舆情详情' : '新增/编辑舆情'" width="75%" destroy-on-close full-modal style="top: 10px">
    <template #footer>
      <a-button style="margin-right: 8px" @click="open = false">{{ readOnly ? '关闭' : '取消' }}</a-button>
      <a-button v-if="readOnly === false" type="primary" @click="onSave">保存</a-button>
    </template>
    <div class="box-border flex">
      <div class="flex-2">
        <div :class="{ 'edit-form': !readOnly }">
          <c-form ref="formRef" :read-only="readOnly" :model="formState" name="basic" autocomplete="off">
            <a-descriptions bordered :label-style="{ width: '120px' }" :content-style="{ width: '200px' }" :column="6" size="small">
              <a-descriptions-item label="舆情原文" :span="6" class="required">
                <a-form-item name="content" :rules="[{ required: true, message: '请输入原文内容!' }]">
                  <div class="relative w-full">
                    <c-textarea
                      v-model:value="formState.content!" :rows="3" class="w-full" placeholder="请输入原文内容"
                      allow-clear
                    />
                  </div>
                </a-form-item>
              </a-descriptions-item>
              <a-descriptions-item label="舆情摘要" :span="6" class="required">
                <a-form-item name="summary" :rules="[{ required: true, message: '请输入内容摘要!' }]">
                  <div class="relative w-full">
                    <c-textarea
                      v-model:value="formState.summary!" :rows="3" class="w-full" placeholder="请输入内容摘要"
                      allow-clear
                    />
                    <a-button
                      v-if="!readOnly" type="link" class="bottom-2 right-2 z-99 !absolute"
                      :loading="aiLoading.summary" @click="aiIdentify(formState.summary, 'summary')"
                    >
                      <template #icon>
                        <c-icon-robot-outlined />
                      </template>
                      AI分析
                    </a-button>
                  </div>
                </a-form-item>
              </a-descriptions-item>
              <a-descriptions-item label="原文链接" :span="3" class="required">
                <a-form-item name="url" :rules="[{ required: true, message: '请输入原文链接!' }]">
                  <c-textarea v-model:value="formState.url!" placeholder="请输入" :rows="2" @change="urlChange" />
                </a-form-item>
                <div v-if="!readOnly" class="text-xs c-error">提示：存在多个链接请回车再输入下一个.</div>
              </a-descriptions-item>
              <a-descriptions-item label="发帖时间" :span="3" class="required">
                <a-form-item name="published" :rules="[{ required: true, message: '请选择发布时间!' }]">
                  <c-date-picker
                    v-model:value="formState.published!" :show-time="{ format: 'HH:mm' }"
                    format="YYYY-MM-DD HH:mm" style="width: 100%;"
                  />
                </a-form-item>
              </a-descriptions-item>
              <a-descriptions-item label="发帖人" :span="2" class="required">
                <a-form-item name="publisher" :rules="[{ required: true, message: '请输入发布者!' }]">
                  <c-input v-model:value="formState.publisher!" placeholder="请输入" />
                </a-form-item>
              </a-descriptions-item>
              <a-descriptions-item label="发帖人id" :span="2" class="required">
                <a-form-item name="sourceId" :rules="[{ required: true, message: '请输入发帖人id!' }]">
                  <c-input v-model:value="formState.sourceId!" placeholder="请输入" />
                </a-form-item>
              </a-descriptions-item>
              <a-descriptions-item label="发帖渠道" :span="2" class="required">
                <a-form-item name="source" :rules="[{ required: true, message: '请输入来源!' }]">
                  <c-select
                    v-model:value="formState.source"
                    :field-names="{ label: 'name', value: 'name' }"
                    :api="api.WebSources.List_GetAsync"
                  />
                </a-form-item>
              </a-descriptions-item>
              <a-descriptions-item label="页面截图" :span="6">
                <a-form-item name="images">
                  <div class="flex gap-2">
                    <div v-for="(img, idx) in formState.images" :key="idx" class="coverBox size-80px overflow-hidden">
                      <c-image
                        :src="joinFilePathById(img)" alt="avatar" :preview="true" :del-ico="true"
                        style="height: 80px; width:80px ; object-fit:cover" @del-image="delImage(img)"
                      />
                    </div>

                    <a-button v-if="!readOnly" type="dashed" block class="!size-80px" @click="avatarUpload">
                      <template #icon>
                        <c-icon-plus-outlined />
                      </template>
                      上传
                    </a-button>
                  </div>
                </a-form-item>
              </a-descriptions-item>
              <a-descriptions-item label="舆情分类" :span="6" class="required">
                <a-form-item name="category" :rules="[{ required: true, message: '请选择事件分类!' }]">
                  <TypeSelect v-model:value="formState.category" v-model:group="formState.mainCategory" @get-children-data="getChildrenData" />
                </a-form-item>
              </a-descriptions-item>
              <template v-if="formState.mainCategory && formState.category">
                <a-descriptions-item label="事故原因" :span="4">
                  <a-form-item name="redundancy1">
                    <div>
                      <TagCheckboxGroup
                        v-model:value="formState.redundancy1"
                        :options="accidentReasonOption ?? []"
                        :disabled="readOnly"
                        @add="openClassificationModal(currentTypeData!, TagType.事故原因)"
                      />
                    </div>
                  </a-form-item>
                </a-descriptions-item>
                <a-descriptions-item label="事故类型(教育厅)" :span="4">
                  <a-form-item name="redundancy2">
                    <div>
                      <TagCheckboxGroup
                        v-model:value="formState.redundancy2"
                        :options="accidentTypeOption ?? []"
                        :disabled="readOnly"
                        @add="openClassificationModal(currentTypeData, TagType.事故类型)"
                      />
                    </div>
                  </a-form-item>
                </a-descriptions-item>
                <a-descriptions-item label="事发地点" :span="4">
                  <a-form-item name="address1">
                    <c-select
                      v-model:value="formState.address1!"
                      :options="adressOption"
                    />
                  </a-form-item>
                </a-descriptions-item>
              </template>
              <a-descriptions-item label="涉及单位" :span="3" class="required">
                <a-form-item name="deptId" :rules="[{ required: true, message: '请输入责任单位!' }]">
                  <C2TreeSelect
                    v-model:value="formState.deptId!"
                  />
                </a-form-item>
              </a-descriptions-item>

              <a-descriptions-item label="发生地(市)" :span="3" class="required">
                <a-form-item name="address" :rules="[{ required: true, message: '请选择发生地点!' }]">
                  <c-standard
                    v-model:value="formState.address" el="select" s-key="county" show-search
                    :field-names="{ label: 'label', value: 'label' }" placeholder="选择事件发生地点"
                    @change="() => { formState.counties = null }"
                  />
                </a-form-item>
              </a-descriptions-item>
              <a-descriptions-item label="所属区县" :span="3">
                <c-standard
                  v-model:value="formState.counties" s-key="county" show-search
                  :field-names="{ label: 'label', value: 'value', options: 'children' }"
                >
                  <template #el="{ loading, options }">
                    <a-form-item v-if="options.find(v => v.label === formState.address)?.children?.length" name="counties">
                      <c-select
                        v-model:value="formState.counties!" :loading="loading"
                        :options="options.find(v => v.label === formState.address)?.children ?? []"
                      />
                    </a-form-item>
                  </template>
                </c-standard>
              </a-descriptions-item>
              <a-descriptions-item label="其他单位名称" :span="6">
                <a-form-item name="department">
                  <c-input v-model:value="formState.department!" placeholder="请输入" />
                </a-form-item>
              </a-descriptions-item>

              <a-descriptions-item label="每日简报舆情" :span="2" class="required">
                <a-form-item name="toDayBriefing" :rules="[{ required: true, message: '请选择!' }]">
                  <a-checkbox-group v-model:value="briefingValue1" :disabled="readOnly" :options="briefingOption" @change="onCheckboxChange" />
                  <a-radio-group
                    v-model:value="briefingValue2"
                    :disabled="readOnly"
                    @change="onRadioChange"
                  >
                    <a-radio :value="ToDayBriefing.否">否</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-descriptions-item>

              <a-descriptions-item label="正面报道" :span="2" class="required">
                <a-form-item name="positive" :rules="[{ required: true, message: '请选择!' }]">
                  <c-enum-select v-model:value="formState.positive" :enum="Positive" placeholder="请输入" />
                </a-form-item>
              </a-descriptions-item>
              <a-descriptions-item label="重大舆情" :span="2" class="required">
                <a-form-item name="serious" :rules="[{ required: true, message: '请选择!' }]">
                  <c-boolean-select v-model:value="formState.serious" placeholder="请选择" />
                </a-form-item>
              </a-descriptions-item>

              <a-descriptions-item v-if="currentTypeData && currentTypeData.isShowNumberPeopleInvolved" label="涉及人数" :span="2">
                <a-form-item name="involved">
                  <c-input-number v-model:value="formState.involved" :min="0" />
                </a-form-item>
              </a-descriptions-item>
              <a-descriptions-item v-if="currentTypeData && currentTypeData.isShowNumberOfDeaths" label="死亡人数" :span="2">
                <a-form-item name="death">
                  <c-input-number v-model:value="formState.death" :min="0" />
                </a-form-item>
              </a-descriptions-item>
              <a-descriptions-item v-if="currentTypeData && currentTypeData.isShowTheInjured" label="受伤人数" :span="2">
                <a-form-item name="injured">
                  <c-input-number v-model:value="formState.injured" :min="0" />
                </a-form-item>
              </a-descriptions-item>

              <template v-if="currentTypeData && currentTypeData.isShowIsHiddenDanger">
                <a-descriptions-item label="安全隐患" :span="2">
                  <a-form-item name="isHiddenDanger">
                    <c-boolean-select v-model:value="formState.isHiddenDanger" disabled />
                  </a-form-item>
                </a-descriptions-item>
                <a-descriptions-item label="隐患类别" :span="2">
                  <a-form-item name="trafficCategory">
                    <div>
                      <TagCheckboxGroup
                        v-model:value="formState.trafficCategory"
                        :options="trafficCategoryOption ?? []"
                        multiple
                        :disabled="readOnly"
                        @add="openClassificationModal(currentTypeData, TagType.隐患类别)"
                      />
                    </div>
                  </a-form-item>
                </a-descriptions-item>
              </template>
            </a-descriptions>
          </c-form>
        </div>
      </div>
      <div class="box-border flex flex-1 flex-col gap-4 pl-4">
        <div v-if="!(readOnly && !topicsList.length)" class="w-100% flex-1 border-1px border-border rounded-xl border-solid">
          <div class="border-1px border-b-border border-b-solid px-4 py-2 text-base font-bold">正在跟踪的专题</div>
          <div class="h-300px overflow-auto px-4 py-2">
            <div v-if="readOnly">
              <div class="c-text-base">
                <a @click="toTopicDatail">
                  {{
                    opinionView.topic?.title }}
                </a>
              </div>
              <div class="c-text-secondary">
                <span>专题概括：</span>
                {{
                  opinionView.topic?.summary }}
              </div>
            </div>
            <a-checkbox-group v-else v-model:value="topicsList" style="width: 100%" :disabled="readOnly" @change="topicsChange">
              <a-row>
                <a-col v-for="item in effectiveTopicsData" :key="item.id!" :span="24" class="mt-4">
                  <a-checkbox :value="item.id">
                    <div class="text-base">{{ item.title }}</div>
                    <div>{{ dateTime(item.start, 'YYYY-MM-DD') }} ~ {{ dateTime(item.end, 'YYYY-MM-DD') }}</div>
                  </a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </div>
        </div>
        <div class="w-100% flex-1 border-1px border-border rounded-xl border-solid">
          <div class="flex items-center justify-between border-1px border-b-border border-b-solid px-4 py-2">
            <div class="text-base font-bold">历史相似舆情</div>
            <div v-if="!readOnly" class="flex-1">
              <a-input-search
                v-model:value="eventParams.summary"
                placeholder="请输入关键词"
                enter-button
                style="width: 100%;margin-left: 16px;"
                @search="getEventData"
              />
            </div>
          </div>
          <div class="h-300px overflow-auto px-4 py-2">
            <div v-if="readOnly">
              <div class="c-text-base">
                <a @click="toEventDatail">
                  {{ opinionView.event?.name }}
                </a>
              </div>
            </div>
            <a-checkbox-group v-else v-model:value="eventList" style="width: 100%" @change="eventChange">
              <a-row>
                <a-col v-for="item in eventData" :key="item.id!" :span="24" class="mt-4">
                  <a-checkbox :value="item.id">
                    <div class="text-base">{{ item.name }}</div>
                  </a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </div>
        </div>
      </div>
    </div>

    <a-modal
      v-model:open="classificationModal.visible"
      title=""
      width="80%"
      :footer="null"
      destroy-on-close
    >
      <Classification
        v-if="classificationModal.visible"
        :type="classificationModal.tagType!"
        :first-value="formState.mainCategory!"
        :level2-value="formState.category!"
        @tag-added="handleTagAdded"
      />
    </a-modal>
  </c-modal>

  <PushReminder v-model:open="pushReminderOpen" :entity-id="currentData.id" :current-department-id="currentData.deptId" :entity-type="DeptPushType.疑似舆情" />
</template>

<script lang='ts' setup>
import type { DepartmentViewModel, PublicOpinionTopicEditModel, PublicOpinionViewModel, TagManageView } from '@/api/models'
import type { SelectProps } from 'ant-design-vue'
import type { StandardModel } from 'ch2-components/lib/standard/types'
import * as api from '@/api'
import { DeptPushType, FileAttribution, FileType, Positive, PublicOpinion, PublicOpinionEditModel, RiskLevel, TagType, ToDayBriefing } from '@/api/models'
import { $auth } from '@/permission'
import { _Role } from '@/permission/RoleName'
import { Guid } from '@/utils/GUID'
import Classification from '@/views/type-tag-management/components/Classification.vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'

const props = defineProps<{
  readOnly?: boolean
  eventName?: string
  isAudit?: boolean
  auditSave?: () => Promise<PublicOpinionViewModel>
}>()

const emit = defineEmits<{
  (e: 'update:open', value: boolean): void
  (e: 'save'): void
}>()

const router = useRouter()

const open = defineModel<boolean>('open', {
  required: true,
  default: false,
})

const formState = defineModel<PublicOpinionEditModel>({
  required: true,
  default: () => (new PublicOpinionEditModel()),

})

const formRef = useTemplateRef('formRef')

const opinionView = computed<PublicOpinionViewModel>(() => {
  return formState.value as PublicOpinionViewModel
})

const pushReminderOpen = ref(false)

watch(() => props.readOnly, (v) => {
  if (v) {
    formRef.value?.baseEl?.clearValidate()
  }
})

const currentData = ref(new PublicOpinion())

async function onSubmit() {
  formState.value.published = dayjs(formState.value.published!)

  const temp = Guid.isNotNull(formState.value.id) ? () => api.OpinionManage.Update_PostAsync(formState.value) : () => api.OpinionManage.Create_PostAsync(formState.value)

  await formRef.value?.baseEl?.validate()

  try {
    const data = await temp()
    currentData.value = viewModelToEditModel(data, new PublicOpinion())
    return data
  }
  catch (err: any) {
    if (err.message)
      message.error(`保存失败：${err.message}`)
    throw err
  }
}

function urlChange(url: string) {
  const { getData } = useCache<StandardModel>(`standard-yqSource`)
  const data = getData() as StandardModel
  if (data) {
    const link = url.split('\n')[0] || ''
    const inputHost = new URL(link).host
    formState.value.source = data.standardItems?.filter(Boolean).find(v => new URL(v.remark!).host === inputHost)?.value
  }
}

function delImage(img: string) {
  formState.value.images = formState.value.images?.filter(item => item !== img)
}

function avatarUpload() {
  useFileMangerModal((files) => {
    formState.value.images = [...formState.value.images ?? [], ...files.map(item => item.id)] as string[]
  }, { multiple: true, immediateReturn: true, menu: [FileType.图片], fileAttribution: FileAttribution.管理认证 })
}

const aiLoading = reactive({
  content: false,
  summary: false,
})

const retryAi = ref(false)

async function aiIdentify(text: string | null | undefined, loading: keyof typeof aiLoading) {
  if (!text)
    return
  aiLoading[loading] = true
  try {
    const jsonData = await api.Ai.GetContent_PostAsync({ context: text }, { timeout: 300000 })

    const { getData } = useCache<StandardModel>(`standard-event_type`)

    const [mainCategory, category] = jsonData['事件类别'].split('-')

    const categoryData = getData() as StandardModel

    if (category && categoryData && mainCategory) {
      const mainCategoryItem = categoryData.standardItems?.find(item => item.label === mainCategory.trim())

      const categoryItem = mainCategoryItem?.children?.find(item => item.label === category.trim())

      if (categoryItem) {
        formState.value.category = categoryItem.value
      }
      else {
        // 再试一次
        if (retryAi.value) {
          return
        }
        retryAi.value = true
        await aiIdentify(text, loading)
        return
      }
    }

    if (jsonData['事件辅类']) {
      let subCategoryRes = [] as string[]
      const subCategorys = jsonData['事件辅类'].split(',').map(v => v.split('-'))
      for (const subCategory of subCategorys) {
        const subCategoryItem = categoryData.standardItems?.find(item => item.label === subCategory[0]?.trim())?.children?.find(item => item.label === subCategory[1].trim())
        if (subCategoryItem) {
          subCategoryRes = [...subCategoryRes, subCategoryItem.value]
        }
      }
    }

    if (jsonData['事发单位']) {
      const { getData } = useCache<DepartmentViewModel[]>('dept-tree')

      const deptData = getData() || []

      const flatDepartments = flattenTree(deptData as any) as DepartmentViewModel[]
      const matchedDepartment = flatDepartments.find(dept => dept.name?.includes(jsonData['事发单位']))
      if (matchedDepartment && matchedDepartment?.id)
        deptChange(matchedDepartment?.id, matchedDepartment)
      formState.value.deptId = matchedDepartment?.id
    }

    if (jsonData['发布者'])
      formState.value.publisher = jsonData['发布者']
    if (jsonData['发布时间'])
      formState.value.published = dayjs(jsonData['发布时间'])
    if (jsonData['舆情类型'])
      formState.value.positive = Positive[jsonData['舆情类型'] as any] as any
    if (jsonData['风险等级'])
      formState.value.riskLevel = RiskLevel[jsonData['风险等级'] as any] as any
    message.success('AI分析成功!')
    aiLoading[loading] = false
  }
  catch (error: any) {
    message.error(error.message)
    aiLoading[loading] = false
  }
}

// 专题相关
const topicsList = ref<GUID[]>([])
const effectiveTopicsData = ref<PublicOpinionTopicEditModel[]>([])

async function getTopicsData() {
  try {
    const res = await api.OpinionTopicManage.GetListAsync({ limit: 999, offset: 0 })
    effectiveTopicsData.value = res.items || []
  }
  catch (error: any) {
    message.error(error.message)
  }
}

function topicsChange(newCheckedList: string[]) {
  if (newCheckedList.length > 1) {
    topicsList.value = Guid.isNotNull(newCheckedList[newCheckedList.length - 1]) ? [newCheckedList[newCheckedList.length - 1]] : []
  }
  formState.value.topicId = topicsList.value[0] ?? Guid.empty
}

// 事件相关
const eventParams = ref({
  summary: '',
  limit: 20,
  offset: 0,
})

const eventData = ref()
const eventList = ref<string[]>([])

async function getEventData() {
  try {
    const res = await api.EventManage.GetListAsync(eventParams.value)
    eventData.value = res.items
  }
  catch (error: any) {
    message.error(error.message)
  }
}

function eventChange(newCheckedList: string[]) {
  if (newCheckedList.length > 1) {
    eventList.value = [newCheckedList[newCheckedList.length - 1]!]
  }
  formState.value.eventId = eventList.value[0] ?? Guid.empty
}

async function onSave() {
  try {
    if (props.isAudit) {
      await formRef.value?.baseEl?.validate()
      currentData.value = await props!.auditSave!()
    }
    else {
      await onSubmit()
      emit('save')
      message.success('保存成功')
    }

    if (currentData.value.dept?.isCooperate && $auth(_Role.舆情监测人员))
      pushReminderOpen.value = true
    open.value = false
  }
  catch (error: any) {
    message.error(`保存失败：${error.message ?? '检查所有字段填写完成'}`)
  }
}

watch(open, async () => {
  if (open.value) {
    eventParams.value.summary = ''
    if (Guid.isNotNull(formState.value.id)) {
      await getData()
    }
    if (!props.readOnly) {
      eventParams.value.summary = opinionView.value.event?.name || props.eventName || ''
      getTopicsData()
      // getEventData()
    }
    // eventList.value = Guid.isNotNull(formState.value.eventId) ? [formState.value.eventId!] : []
    topicsList.value = Guid.isNotNull(formState.value.topicId) ? [formState.value.topicId!] : []
  }
}, {
  immediate: true,
})

function toEventDatail() {
  open.value = false
  router.push({ path: '/event-library/detail', query: { id: opinionView.value.event?.id } })
}

function toTopicDatail() {
  open.value = false
  router.push({ path: '/special-submission', query: { id: opinionView.value.topic?.id } })
}

const accidentTypeOption = ref<SelectProps['options']>([]) // 事故类型

const accidentReasonOption = ref<SelectProps['options']>([]) // 事故原因

const trafficCategoryOption = ref<SelectProps['options']>([]) // 隐患类别

const adressOption = ref<SelectProps['options']>([]) // 事发地点

const currentTypeData = ref<TagManageView>()

/**
 * 根据分类获取事故原因等
 */
function getChildrenData(data: TagManageView) {
  currentTypeData.value = data
  const temp = data.children || []
  accidentTypeOption.value = temp.filter(item => item.tagType === TagType.事故类型)?.map(item => ({ label: item.value, value: item.value }))
  accidentReasonOption.value = temp.filter(item => item.tagType === TagType.事故原因)?.map(item => ({ label: item.value, value: item.value }))
  trafficCategoryOption.value = temp.filter(item => item.tagType === TagType.隐患类别)?.map(item => ({ label: item.value, value: item.value }))
  adressOption.value = temp.filter(item => item.tagType === TagType.事发地点)?.map(item => ({ label: item.value, value: item.value }))
  if (currentTypeData.value?.isShowIsHiddenDanger)
    formState.value.isHiddenDanger = currentTypeData.value.isShowIsHiddenDanger
}

const briefingOption = ref<SelectProps['options']>([
  { label: '涉教师', value: ToDayBriefing.涉教师 },
  { label: '涉高校', value: ToDayBriefing.涉高校 },
]) // 简报类型

const briefingValue1 = ref<ToDayBriefing[]>([])

const briefingValue2 = ref('')

function onRadioChange() {
  formState.value.toDayBriefing = [ToDayBriefing.否]
  briefingValue1.value = []
}

function onCheckboxChange(values: ToDayBriefing[]) {
  formState.value.toDayBriefing = values
  briefingValue2.value = ''
}

const classificationModal = reactive({
  visible: false,
  currentTypeData: null as TagManageView | null,
  tagType: null as TagType | null,
})

function openClassificationModal(typeData: TagManageView, tagType: TagType) {
  classificationModal.currentTypeData = typeData
  classificationModal.visible = true
  classificationModal.tagType = tagType
}

function closeClassificationModal() {
  classificationModal.visible = false
}

function handleTagAdded() {
  closeClassificationModal()
  // 重新拉取标签数据
  if (classificationModal.currentTypeData) {
    getChildrenData(classificationModal.currentTypeData)
  }
}

function getData() {
  return api.OpinionManage.GetAsync({ id: formState.value.id! }).then((res) => {
    formState.value = res
    if (res.toDayBriefing?.includes(ToDayBriefing.否)) {
      briefingValue2.value = ToDayBriefing.否
    }
    else {
      briefingValue1.value = res.toDayBriefing || []
    }
  })
}

defineExpose({
  onSubmit,
})
</script>

<style scoped lang="less">
:deep(.ant-form-item) {
  margin-bottom: 0;
}
.edit-form {
  :deep(.ant-descriptions-item-label.required span) {
    display: flex;

    &::before {
      display: block;
      content: '*';
      color: red;
      padding-right: 4px;
    }
  }
}
</style>
